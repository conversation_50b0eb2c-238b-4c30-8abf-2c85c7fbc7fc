{
  "recommendations": [
    // Essential extensions
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    
    // React/Next.js extensions
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    
    // Git extensions
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    
    // Markdown extensions
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    
    // Utility extensions
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-npm-script",
    "gruntfuggly.todo-tree",
    "streetsidesoftware.code-spell-checker",
    
    // Theme and icons
    "pkief.material-icon-theme",
    "github.github-vscode-theme"
  ]
}
