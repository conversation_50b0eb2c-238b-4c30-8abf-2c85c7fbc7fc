# AI Agent 開發守則

## 項目概述

**項目名稱**: 圖森堡防洪設備企業網站
**技術棧**: Next.js 14 + TypeScript + Tailwind CSS + Contentlayer + next-intl
**目標**: 多語言防洪設備產品展示平台，支持中英文切換

## 核心架構規範

### 技術棧限制
- **必須使用**: Next.js 14 App Router 架構
- **必須使用**: TypeScript 5.4.5，禁止使用 JavaScript
- **必須使用**: Tailwind CSS 進行樣式設計
- **必須使用**: Shadcn UI 組件庫（基於 Radix UI）
- **必須使用**: Contentlayer 管理結構化內容
- **必須使用**: next-intl 處理國際化
- **包管理器**: 必須使用 pnpm，禁止使用 npm 或 yarn

### 文件結構強制規範

```
src/
├── app/[locale]/           # 多語言路由，必須包含 locale 參數
├── components/
│   ├── ui/                 # Shadcn UI 基礎組件，禁止直接修改
│   ├── layout/             # 布局組件（Header, Footer）
│   ├── home/               # 首頁專用組件
│   ├── shared/             # 共享組件
│   └── forms/              # 表單組件
├── lib/                    # 工具函數
├── hooks/                  # 自定義 React hooks
└── types/                  # TypeScript 類型定義
```

### 命名規範（強制執行）
- **組件文件**: PascalCase（`UserProfile.tsx`）
- **頁面文件**: kebab-case（`about-us/page.tsx`）
- **工具函數**: kebab-case（`format-date.ts`）
- **變量函數**: camelCase（`userName`, `getUserData()`）
- **常量**: UPPER_SNAKE_CASE（`API_BASE_URL`）
- **布爾值**: 描述性前綴（`isLoading`, `hasError`, `canEdit`）

## 國際化規範（關鍵）

### 路由結構
- **必須使用**: `[locale]` 動態路由參數
- **支持語言**: `zh`（中文）, `en`（英文）
- **URL 格式**: `/zh/products`, `/en/products`
- **默認語言**: 英文（en）- 與 `src/lib/constants.ts` 中的 `DEFAULT_LOCALE` 保持一致

### 翻譯文件管理
- **位置**: `messages/zh.json`, `messages/en.json`
- **結構**: 按功能模塊組織，使用嵌套對象
- **命名**: 使用 camelCase 鍵名
- **同步要求**: 修改任一語言文件時必須同步更新另一語言文件

### 組件國際化使用
```typescript
// 服務器組件
import { getTranslations } from 'next-intl/server';
const t = await getTranslations('namespace');

// 客戶端組件
import { useTranslations } from 'next-intl';
const t = useTranslations('namespace');
```

### 翻譯規範（嚴格執行）

#### 硬編碼文本禁止規則
- **絕對禁止**: 在組件中直接寫入中文或英文文本
- **絕對禁止**: 在 JSX 中使用字符串字面量作為顯示文本
- **絕對禁止**: 在 alt 屬性、title 屬性中硬編碼文本
- **絕對禁止**: 在表單 placeholder、label 中硬編碼文本
- **絕對禁止**: 在錯誤消息、提示信息中硬編碼文本

#### 正確的翻譯實現
```typescript
// ❌ 錯誤示例 - 硬編碼
<button>提交</button>
<img src="/logo.png" alt="公司標誌" />
<input placeholder="請輸入姓名" />

// ✅ 正確示例 - 使用翻譯
<button>{t('common.submit')}</button>
<img src="/logo.png" alt={t('common.companyLogo')} />
<input placeholder={t('form.enterName')} />
```

#### 翻譯鍵命名規範
- **格式**: 使用點分隔的層級結構（`namespace.section.key`）
- **命名**: 使用 camelCase，描述性命名
- **分組**: 按功能模塊組織（`common`, `navigation`, `product`, `form`）
- **示例**: `product.specifications.title`, `navigation.menu.products`

#### 翻譯文件結構規範
```json
{
  "common": {
    "submit": "提交",
    "cancel": "取消",
    "loading": "加載中...",
    "error": "發生錯誤"
  },
  "navigation": {
    "menu": {
      "home": "首頁",
      "products": "產品",
      "about": "關於我們"
    }
  },
  "product": {
    "specifications": {
      "title": "技術規格",
      "dimensions": "尺寸",
      "weight": "重量"
    }
  }
}
```

## 內容管理規範

### Contentlayer 內容類型
- **Product**: 產品內容（`content/products/zh/`, `content/products/en/`）
- **Article**: 博客文章（`content/blog/zh/`, `content/blog/en/`）
- **Page**: 靜態頁面（`content/pages/zh/`, `content/pages/en/`）

### 內容文件結構
```markdown
---
title: "產品標題"
description: "產品描述"
date: "2024-01-01"
category: "產品分類"
featured: false
---

內容正文使用 MDX 格式
```

### 多語言內容同步
- **強制要求**: 創建中文內容時必須同時創建對應英文內容
- **文件命名**: 相同 slug，不同語言目錄
- **字段一致性**: 所有 frontmatter 字段必須在兩種語言中保持一致

### Contentlayer + next-intl 集成規範

#### 內容翻譯處理策略
**重要說明**: 對於 Contentlayer 管理的長文內容（如產品描述、博客文章），推薦直接使用各語言編寫而非翻譯鍵，因為：
- 長文內容使用翻譯鍵會導致翻譯文件過於龐大
- 內容創作和維護更加困難
- SEO 和內容管理效率較低

```typescript
// ✅ 推薦方式 - 直接使用各語言編寫內容
// content/products/zh/flood-barrier.md
---
title: "先進防洪屏障系統"
description: "用於城市保護的高性能模組化防洪屏障系統"
category: "flood-barriers"
---

# 先進防洪屏障系統
我們的先進防洪屏障系統為城市地區提供快速部署的防洪保護...

// content/products/en/flood-barrier.md
---
title: "Advanced Flood Barrier System"
description: "High-performance modular flood barrier system for urban protection"
category: "flood-barriers"
---

# Advanced Flood Barrier System
Our advanced flood barrier system provides rapid deployment flood protection...
```

#### 混合翻譯策略
- **短文本和 UI 元素**: 使用 next-intl 翻譯鍵
- **長文內容**: 使用 Contentlayer 多語言文件
- **動態標籤和分類**: 使用翻譯鍵確保一致性

#### 內容查詢與翻譯結合
```typescript
// 獲取本地化內容
import { allProducts } from 'contentlayer/generated';
import { getTranslations } from 'next-intl/server';

const getLocalizedProducts = async (locale: string) => {
  const t = await getTranslations('product');

  return allProducts
    .filter(product => product.locale === locale)
    .map(product => ({
      ...product,
      // 翻譯 frontmatter 中的翻譯鍵
      title: t(product.title),
      description: t(product.description)
    }));
};
```

#### 動態內容翻譯規範
- **產品規格**: 使用翻譯鍵而非硬編碼數值單位
- **分類標籤**: 所有分類名稱必須可翻譯
- **狀態文本**: 如 "featured", "new", "popular" 等必須翻譯
- **日期格式**: 使用 next-intl 的日期格式化功能

```typescript
// ❌ 錯誤示例
specifications: {
  weight: "50公斤",
  dimensions: "100x50x30厘米"
}

// ✅ 正確示例
specifications: {
  weight: { value: 50, unit: "kg" },
  dimensions: { length: 100, width: 50, height: 30, unit: "cm" }
}

// 在組件中使用
<span>{spec.weight.value} {t('units.kg')}</span>
<span>{spec.dimensions.length}x{spec.dimensions.width}x{spec.dimensions.height} {t('units.cm')}</span>
```

## 組件開發規範

### 組件結構模板
```typescript
import { FC, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  children?: ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary';
}

export const Component: FC<ComponentProps> = ({
  children,
  className,
  variant = 'primary',
}) => {
  return (
    <div className={cn('base-styles', variant === 'primary' && 'primary-styles', className)}>
      {children}
    </div>
  );
};
```

### 樣式規範
- **必須使用**: Tailwind CSS 工具類
- **必須使用**: `cn()` 函數合併類名
- **響應式**: 移動優先設計（`md:`, `lg:` 斷點）
- **禁止**: 內聯樣式和 CSS-in-JS

### 圖片處理
- **必須使用**: Next.js Image 組件
- **格式支持**: WebP, AVIF 優先
- **必須提供**: alt 屬性
- **路徑**: 統一放置在 `public/images/` 目錄

## 關鍵文件交互規範

### 多文件同步修改要求
1. **修改翻譯文件時**:
   - 修改 `messages/zh.json` → 必須同步修改 `messages/en.json`
   - 添加新翻譯鍵時必須在兩個文件中都添加
   - 刪除翻譯鍵時必須在兩個文件中都刪除
   - 修改翻譯鍵結構時必須保持兩個文件結構一致

2. **修改內容文件時**:
   - 修改 `content/*/zh/*` → 必須同步修改 `content/*/en/*`
   - 保持文件結構和 frontmatter 字段一致
   - 添加新內容字段時必須在兩種語言版本中都添加
   - 修改內容分類或標籤時必須同步更新翻譯文件

3. **修改組件時**:
   - 修改 `src/components/ui/*` → 檢查是否影響其他組件
   - 修改 `src/components/layout/*` → 檢查所有頁面布局

4. **修改配置文件時**:
   - 修改 `contentlayer.config.js` → 檢查內容類型定義，確保 `makeSource` 中包含所有定義的類型
   - 修改 `next.config.js` → 檢查國際化和圖片配置
   - 修改 `tailwind.config.js` → 檢查組件樣式
   - 修改 `src/lib/constants.ts` → 檢查是否有硬編碼文本需要移至翻譯文件

### 關鍵文件硬編碼檢查
修改以下文件時必須檢查硬編碼問題：
- `src/lib/constants.ts` - 所有顯示文本必須使用翻譯鍵
- `src/components/layout/header.tsx` - 品牌名稱和 WhatsApp 文本
- `src/components/layout/footer.tsx` - 版權信息和聯繫方式
- 所有頁面組件的 metadata 生成函數

## AI 決策規範

### 優先級判斷標準
1. **用戶體驗** > 開發便利性
2. **多語言一致性** > 單語言功能完整性
3. **類型安全** > 運行時靈活性
4. **性能優化** > 代碼簡潔性

### 模糊情況處理
- **缺少翻譯時**: 使用默認語言（中文）內容並記錄警告
- **組件變體不明確時**: 使用 `primary` 作為默認變體
- **響應式斷點選擇時**: 優先考慮移動端體驗
- **圖片尺寸不確定時**: 使用響應式圖片並提供多個尺寸

## 禁止事項

### 絕對禁止
- **禁止**: 直接修改 `node_modules/` 中的文件
- **禁止**: 使用 `any` 類型，必須明確類型定義
- **禁止**: 在組件中硬編碼文本，必須使用國際化
- **禁止**: 直接修改 Shadcn UI 組件源碼
- **禁止**: 使用內聯樣式或 styled-components
- **禁止**: 在客戶端組件中使用服務器端 API
- **禁止**: 忽略 TypeScript 錯誤或使用 `@ts-ignore`

### 翻譯相關絕對禁止事項
- **禁止**: 在 JSX 中直接寫入任何語言的文本內容
- **禁止**: 在組件 props 中傳遞硬編碼文本
- **禁止**: 在 console.log 或錯誤消息中使用硬編碼文本
- **禁止**: 在 SEO 相關標籤（title, meta description）中硬編碼
- **禁止**: 在 aria-label, aria-describedby 等無障礙屬性中硬編碼
- **禁止**: 在動態生成的內容中混合硬編碼和翻譯文本
- **禁止**: 跳過翻譯文件同步，單獨修改一種語言
- **禁止**: 使用字符串拼接代替參數化翻譯
- **禁止**: 在常量文件中硬編碼顯示文本（如 `src/lib/constants.ts`）
- **禁止**: 使用條件判斷 locale 來硬編碼不同語言文本

### 常見硬編碼錯誤示例
```typescript
// ❌ 嚴重錯誤 - 條件硬編碼
{locale === 'zh' ? '中文文本' : 'English text'}

// ❌ 錯誤 - 常量中硬編碼
export const SITE_CONFIG = {
  name: 'FloodControl Equipment', // 應該使用翻譯
  description: 'Professional flood control...' // 應該使用翻譯
};

// ❌ 錯誤 - 組件中硬編碼品牌名
<span className="font-semibold">FloodControl</span>

// ✅ 正確 - 使用翻譯
<span className="font-semibold">{t('brand.name')}</span>
```

### 翻譯檢查清單
開發任何功能時必須確認：
- [ ] 所有顯示文本都使用了翻譯函數
- [ ] 翻譯鍵已添加到兩種語言文件中
- [ ] 動態內容（如數量、日期）使用了參數化翻譯
- [ ] 圖片 alt 文本使用了翻譯
- [ ] 表單驗證消息使用了翻譯
- [ ] 錯誤和成功提示使用了翻譯
- [ ] 頁面標題和 meta 描述使用了翻譯
- [ ] 無障礙相關屬性使用了翻譯

### 參數化翻譯規範
```typescript
// ❌ 錯誤 - 字符串拼接
const message = `共找到 ${count} 個產品`;

// ✅ 正確 - 參數化翻譯
const message = t('product.searchResults', { count });

// 翻譯文件中
{
  "product": {
    "searchResults": "共找到 {count} 個產品"
  }
}

// 複數形式處理
{
  "product": {
    "itemCount": {
      "zero": "沒有產品",
      "one": "1 個產品",
      "other": "{count} 個產品"
    }
  }
}
```

### 條件禁止
- **避免**: 在組件中直接使用 `fetch`，應使用專用 hooks
- **避免**: 深層嵌套的組件結構（超過 3 層）
- **避免**: 過大的組件文件（超過 200 行）
- **避免**: 重複的樣式類，應提取為組件變體

## 工作流程規範

### 開發流程
1. **創建功能分支**: `feature/功能名稱`
2. **開發前檢查**: 確認相關翻譯文件和內容文件存在
3. **開發中測試**: 同時測試中英文版本
4. **提交前檢查**: 運行 `pnpm lint` 和 `pnpm type-check`
5. **提交格式**: 使用 Conventional Commits 格式

### 測試要求
- **必須測試**: 兩種語言版本的功能
- **必須測試**: 響應式設計在不同設備上的表現
- **必須測試**: 圖片加載和優化效果
- **必須測試**: 導航和路由功能
- **必須測試**: 翻譯鍵是否正確顯示，無遺漏翻譯
- **必須測試**: 語言切換功能的完整性
- **必須測試**: 參數化翻譯的正確性（數量、日期等）
- **必須測試**: Contentlayer 內容在兩種語言下的正確渲染

### 部署前檢查
- **確認**: 所有翻譯文件同步
- **確認**: 所有圖片資源存在
- **確認**: 構建過程無錯誤
- **確認**: 類型檢查通過
- **確認**: 無硬編碼文本遺留
- **確認**: 所有翻譯鍵都有對應翻譯
- **確認**: 兩種語言版本的內容完整性
- **確認**: Contentlayer 生成的類型無錯誤

## 代碼質量與工具規範

### 代碼注釋規範
- **必須使用**: 英文編寫所有代碼注釋
- **禁止**: 在代碼注釋中使用中文或其他非英文語言
- **格式**: 使用 JSDoc 格式註釋函數和複雜邏輯
- **內容**: 注釋應解釋 "為什麼" 而不是 "是什麼"

```typescript
// ❌ 錯誤 - 中文注釋
// 獲取所有產品
export function getAllProducts() {}

// ✅ 正確 - 英文注釋
/**
 * Retrieves all products filtered by locale
 * @param locale - The locale to filter products by
 * @returns Array of products for the specified locale
 */
export function getAllProducts(locale?: string): Product[] {}
```

### 工具配置要求
- **ESLint**: 必須配置嚴格的 ESLint 規則
- **Prettier**: 必須配置代碼格式化
- **TypeScript**: 必須啟用嚴格模式
- **Biome**: 推薦配置 Biome 進行代碼質量檢查

### 建議的 ESLint 規則增強
```json
{
  "rules": {
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "prefer-const": "error",
    "no-var": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```

## SEO 與 Meta 標籤規範

### 多語言 SEO 要求
- **必須使用**: 每個頁面都要有對應語言的 meta 標籤
- **必須使用**: hreflang 標籤指示語言版本
- **必須使用**: 結構化數據支持多語言

### Meta 標籤實現
```typescript
// 頁面 metadata 必須使用翻譯
export async function generateMetadata({ params: { locale } }: Props) {
  const t = await getTranslations({ locale, namespace: 'meta' });

  return {
    title: t('home.title'),
    description: t('home.description'),
    alternates: {
      canonical: `/${locale}`,
      languages: {
        'zh': '/zh',
        'en': '/en',
      },
    },
  };
}
```

## 性能優化規範

### 強制要求
- **必須使用**: React Server Components 優先
- **必須使用**: Next.js Image 組件進行圖片優化
- **必須使用**: 動態導入進行代碼分割
- **必須避免**: 客戶端組件中的重複渲染

### 推薦實踐
- 使用 `React.memo` 優化組件重渲染
- 合理使用 `useMemo` 和 `useCallback`
- 實施有效的緩存策略
- 優化 Contentlayer 內容查詢

### 圖片和靜態資源國際化
- **圖片命名**: 使用語言無關的命名，避免文字圖片
- **多語言圖片**: 如需要，使用 `/images/zh/` 和 `/images/en/` 分別存放
- **Alt 文本**: 必須使用翻譯函數提供 alt 屬性

---

**重要提醒**: 此規範文件專為 AI Agent 設計，所有規則必須嚴格遵循。遇到規範外情況時，優先保證項目穩定性和用戶體驗。
