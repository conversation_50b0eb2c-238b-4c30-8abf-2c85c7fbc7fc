'use client';

import { Balancer } from '@/components/ui/balancer';
import { Button } from '@/components/ui/button';
import { SlideIn, StaggerContainer, StaggerItem } from '@/components/ui/motion';
import { ScrollIndicator } from '@/components/ui/scroll-indicator';
import { getAllArticles } from '@/lib/content';

import { useTranslations } from 'next-intl';

import { InteractiveHeroBackground } from '@/components/home/<USER>';
import { SmartAnnouncement } from '@/components/home/<USER>';


type HeroProps = {
  locale: string;
};

export const Hero = ({ locale }: HeroProps) => {
  const t = useTranslations('web.home');

  // Get latest articles for announcement
  const allArticles = getAllArticles(locale);
  const latestArticle = allArticles.sort(
    (a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime()
  )[0];

  const scrollToProducts = () => {
    const productSection = document.getElementById('product-portfolio');
    if (productSection) {
      // 等待一帧确保DOM更新完成
      requestAnimationFrame(() => {
        const viewportHeight = window.innerHeight;
        const elementRect = productSection.getBoundingClientRect();
        const currentScrollY = window.pageYOffset;

        // 计算元素相对于文档的位置
        const elementTop = elementRect.top + currentScrollY;
        const elementHeight = elementRect.height;

        // 计算居中位置：元素中心点 - 视窗中心点
        const elementCenter = elementTop + (elementHeight / 2);
        const viewportCenter = viewportHeight / 2;
        const targetScrollY = elementCenter - viewportCenter;

        // 确保不滚动到负值
        const finalScrollY = Math.max(0, targetScrollY);

        window.scrollTo({
          top: finalScrollY,
          behavior: 'smooth'
        });
      });
    }
  };

  return (
    <InteractiveHeroBackground className='w-full'>
      <div className='container mx-auto'>
        <StaggerContainer className='flex flex-col items-center min-h-[80vh] lg:min-h-[85vh] xl:min-h-[90vh] py-20 lg:py-32 xl:py-40'>
          {/* 主要内容区域 - 上移并居中 */}
          <div className='flex-1 flex flex-col items-center justify-center gap-8 lg:gap-12 xl:gap-16 lg:-mt-8 xl:-mt-12'>
            <StaggerItem>
              <SmartAnnouncement locale={locale} latestArticle={latestArticle} />
            </StaggerItem>
            <StaggerItem>
              <div className='flex flex-col gap-4 lg:gap-6'>
                <SlideIn direction='up' delay={0.2}>
                  <h1 className='max-w-2xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
                    <Balancer>{t('meta.title')}</Balancer>
                  </h1>
                </SlideIn>
                <SlideIn direction='up' delay={0.4}>
                  <p className='max-w-2xl text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl'>
                    <Balancer>{t('meta.description')}</Balancer>
                  </p>
                </SlideIn>
              </div>
            </StaggerItem>
          </div>

          {/* 操作按钮区域 - 下移 */}
          <div className='flex-shrink-0 lg:mt-8 xl:mt-12'>
            <StaggerItem>
              <SlideIn direction='up' delay={0.6}>
                <div className='flex flex-col items-center gap-3 lg:gap-4'>
                  <Button size='lg' onClick={scrollToProducts}>
                    {t('hero.cta')}
                  </Button>
                  <ScrollIndicator
                    delay={0.8}
                    text={t('hero.scrollIndicator')}
                    showText={false}
                    onClick={scrollToProducts}
                    className=''
                  />
                </div>
              </SlideIn>
            </StaggerItem>
          </div>
        </StaggerContainer>
      </div>
      {/* Additional spacing for desktop to ensure full viewport coverage */}
      <div className='hidden lg:block h-16 xl:h-24'></div>
    </InteractiveHeroBackground>
  );
};
