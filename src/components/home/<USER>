import { useTranslations } from 'next-intl';

import { MoveRight } from 'lucide-react';

import { Balancer } from '@/components/ui/balancer';
import { Button } from '@/components/ui/button';

export const ProductPortfolio = () => {
  const t = useTranslations('web.home.productPortfolio');

  // 产品占位符图片数据
  const productImages = [
    '/images/products/water-bags-placeholder.jpg',
    '/images/products/abs-barriers-placeholder.jpg',
    '/images/products/aluminum-barriers-placeholder.jpg'
  ];

  return (
    <div id="product-portfolio" className='w-full py-16 lg:pt-32 lg:pb-24 xl:pt-40'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-12'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-6'>
            <div className='flex flex-col gap-4'>
              <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
                <Balancer>{t('title')}</Balancer>
              </h2>
              <p className='max-w-3xl text-center text-lg text-muted-foreground leading-relaxed tracking-tight'>
                {t('description')}
              </p>
            </div>
          </div>

          {/* Products Grid */}
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
            {[0, 1, 2, 3].map((index) => {
              const isComingSoon = index === 3; // 排水泵系统标记为即将推出
              return (
              <div
                key={index}
                className={`group relative overflow-hidden rounded-xl border transition-all duration-300 flex flex-col ${
                  isComingSoon
                    ? 'bg-gray-50/50 border-gray-200 opacity-75'
                    : 'bg-white border-gray-100 hover:shadow-lg hover:scale-[1.02]'
                }`}
              >
                {/* Coming Soon Badge */}
                {isComingSoon && (
                  <div className='absolute top-3 right-3 z-10'>
                    <span className='bg-gray-400 text-white text-xs font-medium px-2 py-1 rounded-full'>
                      即将推出
                    </span>
                  </div>
                )}

                {/* Product Image */}
                <div className={`relative h-64 border-b ${
                  isComingSoon
                    ? 'bg-gray-100 border-gray-200'
                    : 'bg-gray-50 border-gray-200'
                }`}>
                  <div className={`absolute inset-0 bg-gradient-to-br flex items-center justify-center ${
                    isComingSoon
                      ? 'from-gray-200 to-gray-300'
                      : 'from-gray-100 to-gray-200'
                  }`}>
                    <div className={`text-center ${
                      isComingSoon ? 'text-gray-500' : 'text-gray-500'
                    }`}>
                      <div className='text-sm font-medium mb-1'>产品图片</div>
                      <div className='text-xs'>{isComingSoon ? '开发中' : '即将上传'}</div>
                    </div>
                  </div>
                </div>

                {/* Product Content */}
                <div className='p-4 flex flex-col flex-1'>
                  {/* Product Title */}
                  <h3 className={`text-lg font-semibold mb-2 tracking-tight ${
                    isComingSoon ? 'text-gray-600' : 'text-gray-900'
                  }`}>
                    {t(`products.${index}.title`)}
                  </h3>

                  {/* Core Features */}
                  <div className='mb-3'>
                    <div className='space-y-1'>
                      {t(`products.${index}.features`).split('，').map((feature, featureIndex) => (
                        <div
                          key={featureIndex}
                          className={`text-xs flex items-start ${
                            isComingSoon ? 'text-gray-500' : 'text-gray-600'
                          }`}
                        >
                          <span className='mr-1.5 mt-0.5 text-gray-400 flex-shrink-0'>•</span>
                          <span className='flex-1 leading-tight'>{feature.trim()}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Product Description */}
                  <p className={`text-sm leading-tight mb-4 flex-grow ${
                    isComingSoon ? 'text-gray-500' : 'text-gray-600'
                  }`}>
                    {t(`products.${index}.marketPerformance`)}
                  </p>

                  {/* CTA Button */}
                  <div className='mt-auto'>
                    <Button
                      variant={isComingSoon ? 'ghost' : 'default'}
                      size='sm'
                      className={`w-full gap-2 ${
                        isComingSoon
                          ? 'text-gray-500 cursor-default hover:bg-transparent'
                          : 'bg-black hover:bg-gray-800 text-white'
                      }`}
                      disabled={isComingSoon}
                    >
                      {t(`products.${index}.cta`)}
                      {!isComingSoon && (
                        <MoveRight className='h-3 w-3 transition-transform group-hover:translate-x-1' />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
