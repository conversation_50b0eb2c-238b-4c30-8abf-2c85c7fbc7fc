import { Badge } from '@/components/ui/badge';
import { Balancer } from '@/components/ui/balancer';
import { Award, CheckCircle, Globe, Lightbulb, MapPin, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';

export const CompanyStrength = () => {
  const t = useTranslations('web.home.companyStrength');

  return (
    <div className='w-full py-12 lg:py-20 bg-muted/30'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-12'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-6'>
            <div className='flex flex-col gap-4'>
              <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
                <Balancer>{t('title')}</Balancer>
              </h2>
              <div className='flex items-center justify-center gap-4 mt-4'>
                <Badge className='bg-blue-50 border-blue-200 text-blue-700'>
                  技术领先
                </Badge>
                <Badge className='bg-green-50 border-green-200 text-green-700'>
                  全球布局
                </Badge>
                <Badge className='bg-purple-50 border-purple-200 text-purple-700'>
                  行业认可
                </Badge>
              </div>
            </div>
          </div>

          <div className='grid grid-cols-1 gap-8 lg:grid-cols-2'>
            {/* Technical R&D Strength */}
            <div className='relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50/50 to-blue-100/50 border border-blue-200/50 p-8'>
              {/* Header */}
              <div className='flex items-center justify-between mb-8'>
                <div className='flex items-center gap-4'>
                  <div className='flex h-14 w-14 items-center justify-center rounded-xl bg-blue-500/10 border border-blue-200/50'>
                    <Lightbulb className='h-7 w-7 text-blue-600' />
                  </div>
                  <div>
                    <h3 className='text-xl font-semibold tracking-tight text-foreground'>
                      {t('technical.title')}
                    </h3>
                    <p className='text-sm text-muted-foreground'>
                      持续创新，引领行业发展
                    </p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Award className='h-5 w-5 text-blue-600' />
                  <span className='text-sm font-medium text-blue-600'>行业领先</span>
                </div>
              </div>

              {/* Technical Items */}
              <div className='space-y-5'>
                {[0, 1, 2, 3].map((index) => (
                  <div key={index} className='flex items-start gap-4 group'>
                    <div className='flex h-6 w-6 items-center justify-center rounded-full bg-blue-500/20 mt-0.5'>
                      <CheckCircle className='h-4 w-4 text-blue-600' />
                    </div>
                    <p className='text-sm text-muted-foreground leading-relaxed group-hover:text-foreground transition-colors'>
                      {t(`technical.items.${index}`)}
                    </p>
                  </div>
                ))}
              </div>

              {/* Tech Stats */}
              <div className='mt-8 pt-6 border-t border-blue-200/50'>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='text-center'>
                    <div className='text-lg font-bold text-blue-600'>专业</div>
                    <div className='text-xs text-muted-foreground'>研发团队</div>
                  </div>
                  <div className='text-center'>
                    <div className='text-lg font-bold text-blue-600'>国际</div>
                    <div className='text-xs text-muted-foreground'>质量认证</div>
                  </div>
                </div>
              </div>

              {/* Background Pattern */}
              <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/5 to-transparent rounded-full -translate-y-16 translate-x-16'></div>
            </div>

            {/* Market Layout */}
            <div className='relative overflow-hidden rounded-xl bg-gradient-to-br from-green-50/50 to-green-100/50 border border-green-200/50 p-8'>
              {/* Header */}
              <div className='flex items-center justify-between mb-8'>
                <div className='flex items-center gap-4'>
                  <div className='flex h-14 w-14 items-center justify-center rounded-xl bg-green-500/10 border border-green-200/50'>
                    <Globe className='h-7 w-7 text-green-600' />
                  </div>
                  <div>
                    <h3 className='text-xl font-semibold tracking-tight text-foreground'>
                      {t('market.title')}
                    </h3>
                    <p className='text-sm text-muted-foreground'>
                      全球合作，本地服务
                    </p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <MapPin className='h-5 w-5 text-green-600' />
                  <span className='text-sm font-medium text-green-600'>20+国家</span>
                </div>
              </div>

              {/* Market Items */}
              <div className='space-y-5'>
                {[0, 1, 2, 3].map((index) => (
                  <div key={index} className='flex items-start gap-4 group'>
                    <div className='flex h-6 w-6 items-center justify-center rounded-full bg-green-500/20 mt-0.5'>
                      <CheckCircle className='h-4 w-4 text-green-600' />
                    </div>
                    <p className='text-sm text-muted-foreground leading-relaxed group-hover:text-foreground transition-colors'>
                      {t(`market.items.${index}`)}
                    </p>
                  </div>
                ))}
              </div>

              {/* Market Stats */}
              <div className='mt-8 pt-6 border-t border-green-200/50'>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='text-center'>
                    <div className='text-lg font-bold text-green-600'>长期</div>
                    <div className='text-xs text-muted-foreground'>合作关系</div>
                  </div>
                  <div className='text-center'>
                    <div className='text-lg font-bold text-green-600'>本地化</div>
                    <div className='text-xs text-muted-foreground'>技术支持</div>
                  </div>
                </div>
              </div>

              {/* Background Pattern */}
              <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-500/5 to-transparent rounded-full -translate-y-16 translate-x-16'></div>
            </div>
          </div>

          {/* Bottom Summary */}
          <div className='flex flex-col items-center gap-6 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 p-8 border border-primary/20'>
            <div className='flex items-center gap-3'>
              <Zap className='h-6 w-6 text-primary' />
              <h3 className='text-xl font-semibold text-foreground'>
                成功的商业模式和合作价值
              </h3>
            </div>
            <p className='text-center text-muted-foreground max-w-2xl'>
              通过持续的技术创新和全球化布局，我们与合作伙伴建立了长期稳定的关系，
              在多个区域市场都拥有稳定的客户群体，为合作伙伴创造了可观的商业价值。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
