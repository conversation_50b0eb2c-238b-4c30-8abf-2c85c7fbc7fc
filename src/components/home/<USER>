'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Balancer } from '@/components/ui/balancer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { useLocale } from 'next-intl';
import { useState } from 'react';

export const ProfessionalConsultation = () => {
  const locale = useLocale();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setSubmitStatus('success');

    // Reset status after 5 seconds
    setTimeout(() => setSubmitStatus('idle'), 5000);
  };

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-12'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-4'>
            <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
              <Balancer>
                {locale === 'zh' ? '专业技术咨询，深度了解我们的解决方案' : 'Professional Technical Consultation, Deep Understanding of Our Solutions'}
              </Balancer>
            </h2>
            <p className='max-w-4xl text-center text-lg text-muted-foreground leading-relaxed'>
              {locale === 'zh'
                ? '每个防护项目都有其独特性，我们的技术团队随时为您提供专业咨询，帮助您深入了解我们的产品和技术优势。'
                : 'Every protection project has its uniqueness. Our technical team is ready to provide professional consultation to help you understand our products and technical advantages in depth.'
              }
            </p>
          </div>

          {/* Consultation Includes */}
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 max-w-4xl mx-auto'>
            <div className='flex items-center gap-3 rounded-md bg-muted p-4'>
              <CheckCircle className='h-5 w-5 text-primary flex-shrink-0' />
              <p className='text-sm text-muted-foreground'>
                {locale === 'zh' ? '产品技术详细介绍' : 'Detailed product technical introduction'}
              </p>
            </div>
            <div className='flex items-center gap-3 rounded-md bg-muted p-4'>
              <CheckCircle className='h-5 w-5 text-primary flex-shrink-0' />
              <p className='text-sm text-muted-foreground'>
                {locale === 'zh' ? '应用场景分析和建议' : 'Application scenario analysis and recommendations'}
              </p>
            </div>
            <div className='flex items-center gap-3 rounded-md bg-muted p-4'>
              <CheckCircle className='h-5 w-5 text-primary flex-shrink-0' />
              <p className='text-sm text-muted-foreground'>
                {locale === 'zh' ? '定制化方案设计' : 'Customized solution design'}
              </p>
            </div>
            <div className='flex items-center gap-3 rounded-md bg-muted p-4'>
              <CheckCircle className='h-5 w-5 text-primary flex-shrink-0' />
              <p className='text-sm text-muted-foreground'>
                {locale === 'zh' ? '技术支持和服务政策' : 'Technical support and service policies'}
              </p>
            </div>
          </div>

          {/* Contact Form Section */}
          <div className='max-w-2xl mx-auto w-full'>
            <div className='rounded-md bg-muted p-8'>
              {submitStatus === 'success' && (
                <Alert className='mb-6'>
                  <CheckCircle className='h-4 w-4' />
                  <AlertDescription>
                    {locale === 'zh'
                      ? '感谢您的咨询！我们的专业团队将在24小时内与您联系。'
                      : 'Thank you for your inquiry! Our professional team will contact you within 24 hours.'
                    }
                  </AlertDescription>
                </Alert>
              )}

              {submitStatus === 'error' && (
                <Alert variant='destructive' className='mb-6'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>
                    {locale === 'zh' ? '提交失败，请稍后重试。' : 'Submission failed, please try again later.'}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className='space-y-6'>
                {/* Name Field - Different for Chinese and English */}
                {locale === 'zh' ? (
                  <div className='space-y-2'>
                    <Label htmlFor='name'>姓名 *</Label>
                    <Input
                      id='name'
                      name='name'
                      required
                      placeholder='请输入您的姓名'
                    />
                  </div>
                ) : (
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='firstName'>First Name *</Label>
                      <Input
                        id='firstName'
                        name='firstName'
                        required
                        placeholder='Enter your first name'
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor='lastName'>Last Name *</Label>
                      <Input
                        id='lastName'
                        name='lastName'
                        required
                        placeholder='Enter your last name'
                      />
                    </div>
                  </div>
                )}

                <div className='space-y-2'>
                  <Label htmlFor='email'>
                    {locale === 'zh' ? '邮箱' : 'Email Address'} *
                  </Label>
                  <Input
                    id='email'
                    name='email'
                    type='email'
                    required
                    placeholder={locale === 'zh' ? '请输入您的邮箱地址' : 'Enter your email address'}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='company'>
                    {locale === 'zh' ? '公司名称' : 'Company'}
                  </Label>
                  <Input
                    id='company'
                    name='company'
                    placeholder={locale === 'zh' ? '请输入公司名称（可选）' : 'Enter your company name (optional)'}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='region'>
                    {locale === 'zh' ? '所在地区' : 'Region'}
                  </Label>
                  <Input
                    id='region'
                    name='region'
                    placeholder={locale === 'zh' ? '请输入所在地区（可选）' : 'Enter your region (optional)'}
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='requirements'>
                    {locale === 'zh' ? '具体需求' : 'Specific Requirements'}
                  </Label>
                  <Textarea
                    id='requirements'
                    name='requirements'
                    placeholder={locale === 'zh' ? '请描述您的具体需求（可选）' : 'Please describe your specific requirements (optional)'}
                    className='min-h-[100px]'
                  />
                </div>

                <div className='flex items-center justify-between pt-4'>
                  <p className='text-sm text-muted-foreground'>
                    * {locale === 'zh' ? '必填字段' : 'Required fields'}
                  </p>
                  <Button
                    type='submit'
                    disabled={isSubmitting}
                    size='lg'
                    className='min-w-[140px]'
                  >
                    {isSubmitting ? (
                      locale === 'zh' ? '提交中...' : 'Submitting...'
                    ) : (
                      locale === 'zh' ? '获取专业技术咨询' : 'Get Professional Consultation'
                    )}
                  </Button>
                </div>
              </form>

              <p className='text-sm text-muted-foreground text-center mt-6'>
                {locale === 'zh' ? '专业团队将在24小时内与您联系' : 'Our professional team will contact you within 24 hours'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
