import { Balancer } from '@/components/ui/balancer';
import { Button } from '@/components/ui/button';
import { env } from '@/lib/env';
import { MoveRight, PhoneCall } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';




export const CTA = () => {
  const t = useTranslations('web.home.cta');
  const globalT = useTranslations('web.global');

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='flex flex-col items-center gap-8 rounded-md bg-muted p-4 text-center lg:p-14'>
          <div className='flex flex-col gap-2'>
            <h3 className='max-w-xl font-regular text-3xl tracking-tighter md:text-5xl'>
              <Balancer>{t('title')}</Balancer>
            </h3>
            <p className='max-w-xl text-lg text-muted-foreground leading-relaxed tracking-tight'>
              {t('description')}
            </p>
          </div>
          <div className='flex flex-row gap-4'>
            <Button className='gap-4' variant='outline' asChild>
              <Link href='/contact'>
                {globalT('primaryCta')} <PhoneCall className='h-4 w-4' />
              </Link>
            </Button>
            <Button className='gap-4' asChild>
              <Link href={env.NEXT_PUBLIC_APP_URL}>
                {globalT('secondaryCta')} <MoveRight className='h-4 w-4' />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
