import { Badge } from '@/components/ui/badge';
import { Balancer } from '@/components/ui/balancer';
import { AlertTriangle, Building, CheckCircle, Factory, ShoppingCart, Train } from 'lucide-react';
import { useTranslations } from 'next-intl';

export const ApplicationScenarios = () => {
  const t = useTranslations('web.home.applicationScenarios');

  const scenarioIcons = [Building, Factory, ShoppingCart, AlertTriangle, Train];
  const scenarioStats = ['50+', '100+', '广泛', '指定', '可靠'];
  const scenarioColors = [
    'bg-blue-50 border-blue-200 text-blue-700',
    'bg-green-50 border-green-200 text-green-700',
    'bg-orange-50 border-orange-200 text-orange-700',
    'bg-red-50 border-red-200 text-red-700',
    'bg-purple-50 border-purple-200 text-purple-700'
  ];

  return (
    <div className='w-full py-12 lg:py-20'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-12'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-6'>
            <div className='flex flex-col gap-4'>
              <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
                <Balancer>{t('title')}</Balancer>
              </h2>
              <div className='flex items-center justify-center gap-2 mt-4'>
                <CheckCircle className='h-5 w-5 text-green-600' />
                <p className='text-lg text-muted-foreground'>
                  已在多个行业和场景中验证成功
                </p>
              </div>
            </div>
          </div>

          {/* Scenarios Grid */}
          <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3'>
            {[0, 1, 2, 3, 4].map((index) => {
              const Icon = scenarioIcons[index];
              const stat = scenarioStats[index];
              const colorClass = scenarioColors[index];

              return (
                <div
                  key={index}
                  className='group relative overflow-hidden rounded-xl bg-background border p-6 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]'
                >
                  {/* Header with Icon and Badge */}
                  <div className='flex items-start justify-between mb-4'>
                    <div className='flex items-center gap-3'>
                      <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors'>
                        <Icon className='h-6 w-6 text-primary' />
                      </div>
                      <div className='flex flex-col'>
                        <h3 className='text-lg font-semibold tracking-tight'>
                          {t(`scenarios.${index}.title`)}
                        </h3>
                      </div>
                    </div>
                    <Badge className={`${colorClass} font-semibold`}>
                      {stat}
                    </Badge>
                  </div>

                  {/* Description */}
                  <div className='space-y-3'>
                    <p className='text-sm text-muted-foreground leading-relaxed'>
                      {t(`scenarios.${index}.description`)}
                    </p>

                    {/* Success Indicator */}
                    <div className='flex items-center gap-2 pt-2'>
                      <div className='h-2 w-2 rounded-full bg-green-500'></div>
                      <span className='text-xs text-green-600 font-medium'>
                        成功应用案例
                      </span>
                    </div>
                  </div>

                  {/* Hover Effect Overlay */}
                  <div className='absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'></div>
                </div>
              );
            })}
          </div>

          {/* Summary Stats */}
          <div className='flex flex-col items-center gap-6 rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 p-8 border border-primary/20'>
            <div className='text-center'>
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                市场验证数据
              </h3>
              <p className='text-muted-foreground'>
                我们的产品在各个领域都获得了广泛认可和成功应用
              </p>
            </div>

            <div className='grid grid-cols-2 md:grid-cols-4 gap-6 w-full max-w-2xl'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-primary'>50+</div>
                <div className='text-sm text-muted-foreground'>城市社区</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-primary'>100+</div>
                <div className='text-sm text-muted-foreground'>工业园区</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-primary'>多个</div>
                <div className='text-sm text-muted-foreground'>商业区域</div>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-primary'>政府</div>
                <div className='text-sm text-muted-foreground'>指定采购</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
