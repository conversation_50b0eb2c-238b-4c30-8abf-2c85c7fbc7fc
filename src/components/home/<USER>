'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    Carousel,
    type CarouselApi,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel';
import { User } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { Balancer } from '@/components/ui/balancer';


export const Testimonials = () => {
  const t = useTranslations('web.home.testimonials');
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  // Get testimonials items from translations
  const testimonialsItems = [
    {
      title: t('items.0.title'),
      description: t('items.0.description'),
      author: {
        name: t('items.0.author.name'),
        image: t('items.0.author.image'),
      },
    },
    {
      title: t('items.1.title'),
      description: t('items.1.description'),
      author: {
        name: t('items.1.author.name'),
        image: t('items.1.author.image'),
      },
    },
    {
      title: t('items.2.title'),
      description: t('items.2.description'),
      author: {
        name: t('items.2.author.name'),
        image: t('items.2.author.image'),
      },
    },
    {
      title: t('items.3.title'),
      description: t('items.3.description'),
      author: {
        name: t('items.3.author.name'),
        image: t('items.3.author.image'),
      },
    },
  ];

  useEffect(() => {
    if (!api) {
      return;
    }

    setTimeout(() => {
      if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {
        setCurrent(0);
        api.scrollTo(0);
      } else {
        api.scrollNext();
        setCurrent(current + 1);
      }
    }, 4000);
  }, [api, current]);

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-10'>
          <h2 className='text-left font-regular text-3xl tracking-tighter md:text-5xl lg:max-w-xl'>
            <Balancer>{t('title')}</Balancer>
          </h2>
          <Carousel setApi={setApi} className='w-full'>
            <CarouselContent>
              {testimonialsItems.map((item, index) => (
                <CarouselItem className='lg:basis-1/2' key={index}>
                  <div className='flex aspect-video h-full flex-col justify-between rounded-md bg-muted p-6 lg:col-span-2'>
                    <User className='h-8 w-8 stroke-1' />
                    <div className='flex flex-col gap-4'>
                      <div className='flex flex-col'>
                        <h3 className='text-xl tracking-tight'>{item.title}</h3>
                        <p className='max-w-xs text-base text-muted-foreground'>
                          {item.description}
                        </p>
                      </div>
                      <p className='flex flex-row items-center gap-2 text-sm'>
                        <span className='text-muted-foreground'>By</span>
                        <Avatar className='h-6 w-6'>
                          <AvatarImage src={item.author.image} />
                          <AvatarFallback>??</AvatarFallback>
                        </Avatar>
                        <span>{item.author.name}</span>
                      </p>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </div>
  );
};
