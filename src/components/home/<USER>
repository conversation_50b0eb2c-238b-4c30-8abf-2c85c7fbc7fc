import { Badge } from '@/components/ui/badge';
import { Balancer } from '@/components/ui/balancer';
import { Gauge, Lightbulb, Shield, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';

export const ModularSystem = () => {
  const t = useTranslations('web.home.modularSystem');

  // 使用更合适的图标：快速响应、强化防护、主动管理
  const layerIcons = [Zap, Shield, Gauge];

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-10'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-4'>
            <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
              <Balancer>{t('title')}</Balancer>
            </h2>
          </div>

          {/* Industry Insight */}
          <div className='rounded-md bg-muted p-6'>
            <div className='flex items-start gap-4'>
              <div className='flex h-8 w-8 items-center justify-center rounded-md bg-background flex-shrink-0'>
                <Lightbulb className='h-5 w-5 text-primary' />
              </div>
              <div className='flex flex-col gap-2'>
                <h3 className='text-base font-semibold text-foreground'>
                  {t('insightTitle')}
                </h3>
                <p className='text-base text-muted-foreground leading-relaxed'>
                  {t('insight')}
                </p>
              </div>
            </div>
          </div>

          {/* Subtitle */}
          <div className='flex flex-col items-center text-center'>
            <p className='text-lg text-foreground leading-relaxed max-w-3xl'>
              {t('subtitle')}
            </p>
          </div>

          {/* Three Layer Protection System */}
          <div className='grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3'>
            {[0, 1, 2].map((index) => {
              const Icon = layerIcons[index];

              return (
                <div
                  key={index}
                  className='flex flex-col justify-between rounded-md bg-muted p-6'
                >
                  {/* Layer Header */}
                  <div className='flex flex-col gap-4'>
                    <div className='flex items-center gap-3'>
                      <Icon className='h-8 w-8 stroke-1' />
                      <div>
                        <h3 className='text-xl tracking-tight'>
                          {t(`layers.${index}.title`)}
                        </h3>
                        <Badge variant="secondary" className='mt-1 text-xs'>
                          {t('protectionLevel')} {index + 1}
                        </Badge>
                      </div>
                    </div>

                    {/* Layer Details */}
                    <div className='space-y-3'>
                      <div>
                        <p className='text-sm font-medium text-foreground mb-1'>{t('coreProducts')}</p>
                        <p className='text-sm text-muted-foreground'>
                          {t(`layers.${index}.products`)}
                        </p>
                      </div>

                      <div>
                        <p className='text-sm font-medium text-foreground mb-1'>{t('applicationScenario')}</p>
                        <p className='text-sm text-muted-foreground'>
                          {t(`layers.${index}.scenario`)}
                        </p>
                      </div>

                      <div>
                        <p className='text-sm font-medium text-foreground mb-1'>{t('valueAdvantage')}</p>
                        <p className='text-sm text-muted-foreground'>
                          {t(`layers.${index}.value`)}
                        </p>
                      </div>

                      {/* Market Feedback - only for first two layers */}
                      {index < 2 && (
                        <div>
                          <p className='text-sm font-medium text-foreground mb-1'>{t('marketFeedback')}</p>
                          <p className='text-sm text-muted-foreground'>
                            {t(`layers.${index}.feedback`)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>


        </div>
      </div>
    </div>
  );
};
