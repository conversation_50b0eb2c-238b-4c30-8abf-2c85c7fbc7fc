import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { PhoneCall } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

import { Balancer } from '@/components/ui/balancer';


export const FAQ = () => {
  const t = useTranslations('web.home.faq');

  // Get FAQ items from translations
  const faqItems = [
    {
      question: t('items.0.question'),
      answer: t('items.0.answer'),
    },
    {
      question: t('items.1.question'),
      answer: t('items.1.answer'),
    },
    {
      question: t('items.2.question'),
      answer: t('items.2.answer'),
    },
    {
      question: t('items.3.question'),
      answer: t('items.3.answer'),
    },
    {
      question: t('items.4.question'),
      answer: t('items.4.answer'),
    },
  ];

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto'>
        <div className='grid gap-10 lg:grid-cols-2'>
          <div className='flex flex-col gap-10'>
            <div className='flex flex-col gap-4'>
              <div className='flex flex-col gap-2'>
                <h4 className='max-w-xl text-left font-regular text-3xl tracking-tighter md:text-5xl'>
                  <Balancer>{t('title')}</Balancer>
                </h4>
                <p className='max-w-xl text-left text-lg text-muted-foreground leading-relaxed tracking-tight lg:max-w-lg'>
                  {t('description')}
                </p>
              </div>
              <div className=''>
                <Button className='gap-4' variant='outline' asChild>
                  <Link href='/contact'>
                    {t('cta')} <PhoneCall className='h-4 w-4' />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
          <Accordion type='single' collapsible className='w-full'>
            {faqItems.map((item, index) => (
              <AccordionItem key={index} value={`index-${index}`}>
                <AccordionTrigger>{item.question}</AccordionTrigger>
                <AccordionContent>{item.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
};
