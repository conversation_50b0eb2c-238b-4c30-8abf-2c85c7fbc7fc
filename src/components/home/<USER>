'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { MoveRight, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface SmartAnnouncementProps {
  locale: string;
  latestArticle?: {
    slug: string;
    title: string;
    publishDate: string;
  };
}

export const SmartAnnouncement: React.FC<SmartAnnouncementProps> = ({
  locale,
  latestArticle,
}) => {
  const t = useTranslations('web.home');
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // 检查是否有最新文章且用户未关闭
    const dismissed = localStorage.getItem('announcement-dismissed');
    const lastArticleSlug = localStorage.getItem('last-article-slug');
    
    if (latestArticle && !dismissed && lastArticleSlug !== latestArticle.slug) {
      // 延迟显示，避免与其他动画冲突
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [latestArticle]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    // 记住用户的选择
    localStorage.setItem('announcement-dismissed', 'true');
    if (latestArticle) {
      localStorage.setItem('last-article-slug', latestArticle.slug);
    }
  };

  const handleClick = () => {
    // 点击后也记住这篇文章，避免重复显示
    if (latestArticle) {
      localStorage.setItem('last-article-slug', latestArticle.slug);
    }
  };

  if (!latestArticle || isDismissed) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{
            type: 'spring',
            stiffness: 200,
            damping: 20,
            duration: 0.6,
          }}
          className="relative"
        >
          <Button 
            variant='secondary' 
            size='sm' 
            className='gap-3 pr-8 relative group hover:shadow-md transition-all duration-200' 
            asChild
          >
            <Link href={`/${locale}/blog/${latestArticle.slug}`} onClick={handleClick}>
              <span className="text-xs font-medium">{t('hero.announcement')}</span>
              <MoveRight className='h-3 w-3 group-hover:translate-x-0.5 transition-transform duration-200' />
            </Link>
          </Button>
          
          {/* 关闭按钮 */}
          <button
            onClick={handleDismiss}
            className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-muted hover:bg-muted-foreground/20 flex items-center justify-center transition-colors duration-200 opacity-0 group-hover:opacity-100"
            aria-label="关闭公告"
          >
            <X className="w-3 h-3 text-muted-foreground" />
          </button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
