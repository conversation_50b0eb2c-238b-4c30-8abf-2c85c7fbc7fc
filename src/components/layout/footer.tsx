import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface FooterProps {
  locale: string;
}

export function Footer({ locale }: FooterProps) {
  const t = useTranslations('web.header');

  const navigationItems = [
    {
      title: 'Home',
      href: `/${locale}`,
      description: '',
    },
    {
      title: 'Pages',
      description: 'Managing a small business today is already tough.',
      items: [
        {
          title: 'Blog',
          href: `/${locale}/blog`,
        },
        {
          title: 'Contact',
          href: `/${locale}/contact`,
        },
      ],
    },
    {
      title: 'Legal',
      description: 'We stay on top of the latest legal requirements.',
      items: [
        {
          title: 'Privacy Policy',
          href: `/${locale}/legal/privacy`,
        },
        {
          title: 'Terms of Service',
          href: `/${locale}/legal/terms`,
        },
      ],
    },
  ];

  // 页脚内容组件
  const FooterContent = () => (
    <div className='grid items-center gap-10 lg:grid-cols-2'>
      <div className='flex flex-col items-start gap-8'>
        <div className='flex flex-col gap-2'>
          <h2 className='max-w-xl text-left font-regular text-3xl tracking-tighter md:text-5xl'>
            FloodControl
          </h2>
          <p className='max-w-lg text-left text-muted-foreground text-lg leading-relaxed tracking-tight'>
            Professional flood control equipment and solutions.
          </p>
        </div>
      </div>
      <div className='grid items-start gap-10 lg:grid-cols-3'>
        {navigationItems.map((item) => (
          <div
            key={item.title}
            className='flex flex-col items-start gap-1 text-base'
          >
            <div className='flex flex-col gap-2'>
              {item.href ? (
                <Link
                  href={item.href}
                  className='flex items-center justify-between'
                  target={
                    item.href.includes('http') ? '_blank' : undefined
                  }
                  rel={
                    item.href.includes('http')
                      ? 'noopener noreferrer'
                      : undefined
                  }
                >
                  <span className='text-xl'>{item.title}</span>
                </Link>
              ) : (
                <p className='text-xl'>{item.title}</p>
              )}
              {item.items?.map((subItem) => (
                <Link
                  key={subItem.title}
                  href={subItem.href}
                  className='flex items-center justify-between'
                  target={
                    subItem.href.includes('http') ? '_blank' : undefined
                  }
                  rel={
                    subItem.href.includes('http')
                      ? 'noopener noreferrer'
                      : undefined
                  }
                >
                  <span className='text-muted-foreground'>
                    {subItem.title}
                  </span>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className='space-y-8'>
      {/* 方案选择器 - 临时用于演示 */}
      <div className='container mx-auto py-4 border-t border-border bg-muted/50'>
        <div className='text-center'>
          <p className='text-sm text-muted-foreground mb-4'>
            🎨 页脚设计方案预览（请向下滚动查看三个不同方案）
          </p>
        </div>
      </div>

      {/* 方案1：极简白色背景 */}
      <div>
        <div className='container mx-auto py-2 bg-blue-50 border border-blue-200 rounded-md'>
          <p className='text-center text-sm font-medium text-blue-800'>
            方案1：极简白色背景 - 与首页完全一致的纯净风格
          </p>
        </div>
        <section className='border-t border-border'>
          <div className='w-full bg-background py-20 text-foreground lg:py-40'>
            <div className='container mx-auto'>
              <FooterContent />
            </div>
          </div>
        </section>
      </div>

      {/* 方案2：微妙灰色背景 */}
      <div>
        <div className='container mx-auto py-2 bg-green-50 border border-green-200 rounded-md'>
          <p className='text-center text-sm font-medium text-green-800'>
            方案2：微妙灰色背景 - 与首页muted区块风格一致
          </p>
        </div>
        <section className='border-t border-border'>
          <div className='w-full bg-muted py-20 text-foreground lg:py-40'>
            <div className='container mx-auto'>
              <FooterContent />
            </div>
          </div>
        </section>
      </div>

      {/* 方案4：卡片式设计 */}
      <div>
        <div className='container mx-auto py-2 bg-purple-50 border border-purple-200 rounded-md'>
          <p className='text-center text-sm font-medium text-purple-800'>
            方案4：卡片式设计 - 现代感强，层次清晰
          </p>
        </div>
        <section className='py-20 lg:py-40'>
          <div className='container mx-auto'>
            <div className='rounded-lg border bg-card p-8 lg:p-12 shadow-sm'>
              <FooterContent />
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
