'use client';

import { LanguageSwitcher } from '@/components/shared';
import { Button } from '@/components/ui/button';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { Menu, MoveRight, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState } from 'react';

interface HeaderProps {
  locale: string;
}

export function Header({ locale }: HeaderProps) {
  const t = useTranslations('web.header');
  const globalT = useTranslations('web.global');

  const navigationItems = [
    {
      title: t('home'),
      href: `/${locale}`,
      description: '',
    },
    {
      title: t('product.title'),
      description: t('product.description'),
      items: [
        {
          title: t('product.center'),
          href: `/${locale}/products`,
        },
        {
          title: t('product.selfPrimingBags'),
          href: `/${locale}/products/self-priming-water-bags`,
        },
        {
          title: t('product.absBarriers'),
          href: `/${locale}/products/abs-flood-barriers`,
        },
        {
          title: t('product.aluminumBarriers'),
          href: `/${locale}/products/aluminum-custom-barriers`,
        },
        {
          title: t('product.drainagePumps'),
          href: `/${locale}/products/drainage-pumps`,
        },
      ],
    },
    {
      title: t('blog'),
      href: `/${locale}/blog`,
      description: '',
    },
    {
      title: t('about'),
      href: `/${locale}/about`,
      description: '',
    },
  ];

  const [isOpen, setOpen] = useState(false);

  return (
    <header className='sticky top-0 left-0 z-40 w-full border-b bg-background'>
      <div className='container relative mx-auto flex min-h-20 flex-row items-center gap-4 px-6 lg:grid lg:grid-cols-3 lg:px-8 xl:px-12'>
        {/* Logo - 左侧 */}
        <div className='flex items-center justify-start'>
          <span className='whitespace-nowrap font-bold text-xl'>FloodControl</span>
        </div>

        {/* 核心导航 - 居中 */}
        <div className='hidden flex-row items-center justify-center lg:flex'>
          <NavigationMenu className='flex items-center justify-center'>
            <NavigationMenuList className='flex flex-row justify-center gap-2'>
              {navigationItems.map((item) => (
                <NavigationMenuItem key={item.title}>
                  {item.href ? (
                    <>
                      <NavigationMenuLink asChild>
                        <Button variant='ghost' asChild>
                          <Link href={item.href}>{item.title}</Link>
                        </Button>
                      </NavigationMenuLink>
                    </>
                  ) : (
                    <>
                      <NavigationMenuTrigger className='font-medium text-sm'>
                        {item.title}
                      </NavigationMenuTrigger>
                      <NavigationMenuContent className='!w-[450px] p-4'>
                        <div className='flex grid-cols-2 flex-col gap-4 lg:grid'>
                          <div className='flex h-full flex-col justify-between'>
                            <div className='flex flex-col'>
                              <p className='text-base'>{item.title}</p>
                              <p className='text-muted-foreground text-sm'>
                                {item.description}
                              </p>
                            </div>
                            <Button size='sm' className='mt-10' asChild>
                              <Link href={`/${locale}/contact`}>
                                {globalT('primaryCta')}
                              </Link>
                            </Button>
                          </div>
                          <div className='flex h-full flex-col justify-end text-sm'>
                            {item.items?.map((subItem, idx) => (
                              <NavigationMenuLink
                                href={subItem.href}
                                key={idx}
                                className='flex flex-row items-center justify-between rounded px-4 py-2 hover:bg-muted'
                              >
                                <span>{subItem.title}</span>
                                <MoveRight className='h-4 w-4 text-muted-foreground' />
                              </NavigationMenuLink>
                            ))}
                          </div>
                        </div>
                      </NavigationMenuContent>
                    </>
                  )}
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* 右侧功能区 - 紧凑化 */}
        <div className='flex w-full justify-end gap-2'>
          <div className='hidden md:inline'>
            <LanguageSwitcher currentLocale={locale} />
          </div>
          <div className='hidden md:inline'>
            <ThemeToggle />
          </div>
          <div className='hidden border-r md:inline mx-1' />
          <Button asChild className='bg-black hover:bg-gray-800 text-white'>
            <Link
              href={`https://wa.me/15551234567?text=${encodeURIComponent(
                locale === 'zh'
                  ? '您好，我想咨询防汛设备相关信息'
                  : 'Hello, I would like to inquire about flood control equipment'
              )}`}
              target='_blank'
              rel='noopener noreferrer'
            >
              {locale === 'zh' ? 'WhatsApp咨询' : 'WhatsApp Us'}
            </Link>
          </Button>
        </div>
        <div className='flex w-12 shrink items-end justify-end lg:hidden'>
          <Button variant='ghost' onClick={() => setOpen(!isOpen)}>
            {isOpen ? <X className='h-5 w-5' /> : <Menu className='h-5 w-5' />}
          </Button>
          {isOpen && (
            <div className='container absolute top-20 right-0 flex w-full flex-col gap-8 border-t bg-background py-4 px-6 shadow-lg lg:px-8 xl:px-12'>
              {navigationItems.map((item) => (
                <div key={item.title}>
                  <div className='flex flex-col gap-2'>
                    {item.href ? (
                      <Link
                        href={item.href}
                        className='flex items-center justify-between'
                        target={
                          item.href.startsWith('http') ? '_blank' : undefined
                        }
                        rel={
                          item.href.startsWith('http')
                            ? 'noopener noreferrer'
                            : undefined
                        }
                      >
                        <span className='text-lg'>{item.title}</span>
                        <MoveRight className='h-4 w-4 stroke-1 text-muted-foreground' />
                      </Link>
                    ) : (
                      <p className='text-lg'>{item.title}</p>
                    )}
                    {item.items?.map((subItem) => (
                      <Link
                        key={subItem.title}
                        href={subItem.href}
                        className='flex items-center justify-between'
                      >
                        <span className='text-muted-foreground'>
                          {subItem.title}
                        </span>
                        <MoveRight className='h-4 w-4 stroke-1' />
                      </Link>
                    ))}
                  </div>
                </div>
              ))}

              {/* WhatsApp按钮 - 移动端 */}
              <div className='border-t pt-4'>
                <Link
                  href={`https://wa.me/15551234567?text=${encodeURIComponent(
                    locale === 'zh'
                      ? '您好，我想咨询防汛设备相关信息'
                      : 'Hello, I would like to inquire about flood control equipment'
                  )}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='flex items-center justify-between bg-black hover:bg-gray-800 text-white px-4 py-3 rounded-md'
                >
                  <span className='text-lg font-medium'>
                    {locale === 'zh' ? 'WhatsApp咨询' : 'WhatsApp Us'}
                  </span>
                  <MoveRight className='h-4 w-4 stroke-1' />
                </Link>
              </div>

              {/* 语言和主题切换 - 移动端 */}
              <div className='flex items-center justify-between border-t pt-4'>
                <LanguageSwitcher currentLocale={locale} />
                <ThemeToggle />
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
