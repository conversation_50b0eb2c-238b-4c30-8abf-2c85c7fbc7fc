'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScrollIndicatorProps {
  className?: string;
  delay?: number;
  text?: string;
  showText?: boolean;
  onClick?: () => void;
}

export const ScrollIndicator: React.FC<ScrollIndicatorProps> = ({
  className,
  delay = 0.8,
  text,
  showText = false,
  onClick,
}) => {
  // Container animation - fade in after delay
  const containerVariants = {
    hidden: {
      opacity: 0,
      y: 10,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay,
        ease: 'easeOut',
      },
    },
  };

  // Floating animation for the chevron icon
  const floatingVariants = {
    animate: {
      y: [0, 4, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <motion.div
      className={cn(
        'flex flex-col items-center justify-center gap-2',
        onClick && 'cursor-pointer',
        className
      )}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      onClick={onClick}
    >
      {/* Text label (optional) */}
      {showText && text && (
        <motion.p
          className="text-sm text-muted-foreground font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.3, duration: 0.4 }}
        >
          {text}
        </motion.p>
      )}

      {/* Simple animated scroll indicator */}
      <motion.div
        className="flex items-center justify-center"
        variants={floatingVariants}
        animate="animate"
        whileHover={onClick ? { scale: 1.1, y: -1 } : {}}
        whileTap={onClick ? { scale: 0.9 } : {}}
      >
        <ChevronDown className="w-6 h-6 text-muted-foreground/60 hover:text-primary transition-colors duration-200" />
      </motion.div>
    </motion.div>
  );
};
