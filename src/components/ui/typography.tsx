'use client';

import { ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { Balancer } from './balancer';

const headingVariants = cva(
  'font-regular tracking-tighter',
  {
    variants: {
      size: {
        h1: 'text-5xl md:text-7xl',
        h2: 'text-3xl md:text-5xl',
        h3: 'text-2xl md:text-4xl',
        h4: 'text-xl md:text-2xl',
        h5: 'text-lg md:text-xl',
        h6: 'text-base md:text-lg',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
      },
    },
    defaultVariants: {
      size: 'h2',
      align: 'left',
    },
  }
);

const textVariants = cva(
  'leading-relaxed tracking-tight',
  {
    variants: {
      size: {
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg md:text-xl',
        xl: 'text-xl md:text-2xl',
      },
      color: {
        default: 'text-foreground',
        muted: 'text-muted-foreground',
        primary: 'text-primary',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
      },
    },
    defaultVariants: {
      size: 'base',
      color: 'default',
      align: 'left',
    },
  }
);

interface HeadingProps extends VariantProps<typeof headingVariants> {
  children: ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  balanced?: boolean;
  maxWidth?: string;
}

interface TextProps extends VariantProps<typeof textVariants> {
  children: ReactNode;
  className?: string;
  as?: 'p' | 'span' | 'div';
  balanced?: boolean;
  maxWidth?: string;
}

export function Heading({
  children,
  className,
  size,
  align,
  as = 'h2',
  balanced = true,
  maxWidth,
  ...props
}: HeadingProps) {
  const Component = as;
  const content = balanced ? <Balancer>{children}</Balancer> : children;
  
  return (
    <Component
      className={cn(
        headingVariants({ size, align }),
        maxWidth && `max-w-${maxWidth}`,
        className
      )}
      {...props}
    >
      {content}
    </Component>
  );
}

export function Text({
  children,
  className,
  size,
  color,
  align,
  as = 'p',
  balanced = false,
  maxWidth,
  ...props
}: TextProps) {
  const Component = as;
  const content = balanced ? <Balancer>{children}</Balancer> : children;
  
  return (
    <Component
      className={cn(
        textVariants({ size, color, align }),
        maxWidth && `max-w-${maxWidth}`,
        className
      )}
      {...props}
    >
      {content}
    </Component>
  );
}

// Convenience components for common use cases
export function HeroHeading({ children, className, ...props }: Omit<HeadingProps, 'size' | 'as'>) {
  return (
    <Heading
      as="h1"
      size="h1"
      align="center"
      maxWidth="2xl"
      className={cn('font-regular', className)}
      {...props}
    >
      {children}
    </Heading>
  );
}

export function SectionHeading({ children, className, ...props }: Omit<HeadingProps, 'size' | 'as'>) {
  return (
    <Heading
      as="h2"
      size="h2"
      maxWidth="xl"
      className={className}
      {...props}
    >
      {children}
    </Heading>
  );
}

export function HeroText({ children, className, ...props }: Omit<TextProps, 'size' | 'color'>) {
  return (
    <Text
      size="lg"
      color="muted"
      align="center"
      maxWidth="2xl"
      balanced
      className={className}
      {...props}
    >
      {children}
    </Text>
  );
}

export function SectionText({ children, className, ...props }: Omit<TextProps, 'size' | 'color'>) {
  return (
    <Text
      size="lg"
      color="muted"
      maxWidth="xl"
      className={cn('lg:max-w-lg', className)}
      {...props}
    >
      {children}
    </Text>
  );
}
