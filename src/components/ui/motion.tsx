'use client';

/**
 * Reusable motion components with predefined animations
 * These components provide consistent animation behavior across the application
 */

import React from 'react';
import { motion, HTMLMotionProps, Variants } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  fadeVariants,
  slideVariants,
  scaleVariants,
  hoverVariants,
  cardVariants,
  staggerContainer,
  staggerItem,
} from '@/lib/animations';

// Base motion component props
interface BaseMotionProps {
  className?: string;
  children: React.ReactNode;
}

// Fade In component
interface FadeInProps extends BaseMotionProps {
  delay?: number;
  duration?: number;
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  className,
  delay = 0,
  duration = 0.5,
}) => {
  const variants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration,
        delay,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

// Slide In component
interface SlideInProps extends BaseMotionProps {
  direction?: 'left' | 'right' | 'up' | 'down';
  delay?: number;
  distance?: number;
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  className,
  direction = 'up',
  delay = 0,
  distance = 50,
}) => {
  const getVariants = (): Variants => {
    const getInitialPosition = () => {
      switch (direction) {
        case 'left':
          return { x: -distance, y: 0 };
        case 'right':
          return { x: distance, y: 0 };
        case 'up':
          return { x: 0, y: distance };
        case 'down':
          return { x: 0, y: -distance };
        default:
          return { x: 0, y: distance };
      }
    };

    const initialPos = getInitialPosition();

    return {
      hidden: {
        opacity: 0,
        ...initialPos,
      },
      visible: {
        opacity: 1,
        x: 0,
        y: 0,
        transition: {
          type: 'spring',
          stiffness: 100,
          damping: 15,
          delay,
        },
      },
    };
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={getVariants()}
    >
      {children}
    </motion.div>
  );
};

// Scale In component
interface ScaleInProps extends BaseMotionProps {
  delay?: number;
  scale?: number;
}

export const ScaleIn: React.FC<ScaleInProps> = ({
  children,
  className,
  delay = 0,
  scale = 0.8,
}) => {
  const variants: Variants = {
    hidden: {
      opacity: 0,
      scale,
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
        delay,
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

// Hover Scale component
interface HoverScaleProps extends BaseMotionProps {
  scale?: number;
  duration?: number;
}

export const HoverScale: React.FC<HoverScaleProps> = ({
  children,
  className,
  scale = 1.05,
  duration = 0.2,
}) => {
  return (
    <motion.div
      className={cn('cursor-pointer', className)}
      whileHover={{
        scale,
        transition: { duration, ease: 'easeOut' },
      }}
      whileTap={{
        scale: 0.95,
        transition: { duration: 0.1, ease: 'easeOut' },
      }}
    >
      {children}
    </motion.div>
  );
};

// Stagger Container component
interface StaggerContainerProps extends BaseMotionProps {
  staggerDelay?: number;
  childrenDelay?: number;
}

export const StaggerContainer: React.FC<StaggerContainerProps> = ({
  children,
  className,
  staggerDelay = 0.1,
  childrenDelay = 0.2,
}) => {
  const variants: Variants = {
    hidden: {
      opacity: 0,
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: childrenDelay,
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

// Stagger Item component
interface StaggerItemProps extends BaseMotionProps {
  y?: number;
}

export const StaggerItem: React.FC<StaggerItemProps> = ({
  children,
  className,
  y = 20,
}) => {
  const variants: Variants = {
    hidden: {
      opacity: 0,
      y,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
      },
    },
  };

  return (
    <motion.div className={className} variants={variants}>
      {children}
    </motion.div>
  );
};

// Animated Card component
interface AnimatedCardProps extends BaseMotionProps {
  hoverEffect?: boolean;
  delay?: number;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  className,
  hoverEffect = true,
  delay = 0,
}) => {
  const variants: Variants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15,
        delay,
      },
    },
  };

  const hoverVariants = hoverEffect
    ? {
        y: -5,
        scale: 1.02,
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 25,
        },
      }
    : {};

  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={variants}
      whileHover={hoverVariants}
    >
      {children}
    </motion.div>
  );
};

// Reveal on Scroll component (using Intersection Observer)
interface RevealOnScrollProps extends BaseMotionProps {
  threshold?: number;
  triggerOnce?: boolean;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
}

export const RevealOnScroll: React.FC<RevealOnScrollProps> = ({
  children,
  className,
  threshold = 0.1,
  triggerOnce = true,
  direction = 'up',
  delay = 0,
}) => {
  const getVariants = (): Variants => {
    const getInitialPosition = () => {
      switch (direction) {
        case 'left':
          return { x: -50, y: 0 };
        case 'right':
          return { x: 50, y: 0 };
        case 'up':
          return { x: 0, y: 50 };
        case 'down':
          return { x: 0, y: -50 };
        default:
          return { x: 0, y: 50 };
      }
    };

    const initialPos = getInitialPosition();

    return {
      hidden: {
        opacity: 0,
        ...initialPos,
      },
      visible: {
        opacity: 1,
        x: 0,
        y: 0,
        transition: {
          type: 'spring',
          stiffness: 100,
          damping: 15,
          delay,
        },
      },
    };
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: triggerOnce, amount: threshold }}
      variants={getVariants()}
    >
      {children}
    </motion.div>
  );
};

// Floating animation component
interface FloatingProps extends BaseMotionProps {
  duration?: number;
  yOffset?: number;
}

export const Floating: React.FC<FloatingProps> = ({
  children,
  className,
  duration = 3,
  yOffset = 10,
}) => {
  return (
    <motion.div
      className={className}
      animate={{
        y: [-yOffset, yOffset, -yOffset],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      {children}
    </motion.div>
  );
};
