'use client';

/**
 * Page transition component for smooth navigation animations
 * Provides consistent page enter/exit animations across the application
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { pageVariants, transitions } from '@/lib/animations';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  key?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  key,
}) => {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={key}
        className={className}
        initial="initial"
        animate="in"
        exit="out"
        variants={pageVariants}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};

// Layout transition for consistent layout animations
interface LayoutTransitionProps {
  children: React.ReactNode;
  className?: string;
}

export const LayoutTransition: React.FC<LayoutTransitionProps> = ({
  children,
  className,
}) => {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={transitions.smooth}
      layout
    >
      {children}
    </motion.div>
  );
};

// Section transition for individual page sections
interface SectionTransitionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

export const SectionTransition: React.FC<SectionTransitionProps> = ({
  children,
  className,
  delay = 0,
  direction = 'up',
}) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left':
        return { x: -50, y: 0 };
      case 'right':
        return { x: 50, y: 0 };
      case 'up':
        return { x: 0, y: 50 };
      case 'down':
        return { x: 0, y: -50 };
      default:
        return { x: 0, y: 50 };
    }
  };

  const initialPos = getInitialPosition();

  return (
    <motion.section
      className={className}
      initial={{
        opacity: 0,
        ...initialPos,
      }}
      whileInView={{
        opacity: 1,
        x: 0,
        y: 0,
      }}
      viewport={{ once: true, amount: 0.1 }}
      transition={{
        ...transitions.smooth,
        delay,
      }}
    >
      {children}
    </motion.section>
  );
};
