'use client';

import { ReactNode } from 'react';
import ReactWrapBalancer from 'react-wrap-balancer';

interface BalancerProps {
  children: ReactNode;
  className?: string;
  /**
   * The ratio of "balance-ness" vs. "natural-ness".
   * 0 = no balancing, 1 = full balancing
   * @default 1
   */
  ratio?: number;
  /**
   * Prefer native CSS text-wrap: balance when supported
   * @default true
   */
  preferNative?: boolean;
}

export function Balancer({
  children,
  className,
  ratio = 1,
  preferNative = true
}: BalancerProps) {
  return (
    <ReactWrapBalancer
      ratio={ratio}
      preferNative={preferNative}
      className={className}
    >
      {children}
    </ReactWrapBalancer>
  );
}
