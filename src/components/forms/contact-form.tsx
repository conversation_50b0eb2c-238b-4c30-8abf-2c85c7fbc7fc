'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Alert,
  AlertDescription,
} from '@/components/ui';
import { CheckCircle, AlertCircle } from 'lucide-react';

interface ContactFormProps {
  locale: string;
}

export function ContactForm({ locale }: ContactFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setSubmitStatus('success');

    // Reset status after 5 seconds
    setTimeout(() => setSubmitStatus('idle'), 5000);
  };

  return (
    <Card className='w-full max-w-2xl mx-auto'>
      <CardHeader>
        <CardTitle>Contact Us</CardTitle>
        <CardDescription>
          Get in touch with our flood control experts. We'll respond within 24
          hours.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {submitStatus === 'success' && (
          <Alert className='mb-6'>
            <CheckCircle className='h-4 w-4' />
            <AlertDescription>
              Thank you for your message! We'll get back to you soon.
            </AlertDescription>
          </Alert>
        )}

        {submitStatus === 'error' && (
          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription>
              Something went wrong. Please try again later.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='firstName'>First Name *</Label>
              <Input
                id='firstName'
                name='firstName'
                required
                placeholder='Enter your first name'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='lastName'>Last Name *</Label>
              <Input
                id='lastName'
                name='lastName'
                required
                placeholder='Enter your last name'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='email'>Email Address *</Label>
            <Input
              id='email'
              name='email'
              type='email'
              required
              placeholder='Enter your email address'
            />
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='company'>Company</Label>
              <Input
                id='company'
                name='company'
                placeholder='Enter your company name'
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='phone'>Phone Number</Label>
              <Input
                id='phone'
                name='phone'
                type='tel'
                placeholder='Enter your phone number'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='inquiryType'>Inquiry Type</Label>
            <Select name='inquiryType'>
              <SelectTrigger>
                <SelectValue placeholder='Select inquiry type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='general'>General Inquiry</SelectItem>
                <SelectItem value='quote'>Request Quote</SelectItem>
                <SelectItem value='support'>Technical Support</SelectItem>
                <SelectItem value='partnership'>Partnership</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='subject'>Subject *</Label>
            <Input
              id='subject'
              name='subject'
              required
              placeholder='Enter the subject of your inquiry'
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='message'>Message *</Label>
            <Textarea
              id='message'
              name='message'
              required
              placeholder='Please describe your inquiry in detail...'
              className='min-h-[120px]'
            />
          </div>

          <div className='flex items-center justify-between'>
            <p className='text-sm text-muted-foreground'>* Required fields</p>
            <Button
              type='submit'
              disabled={isSubmitting}
              className='min-w-[120px]'
            >
              {isSubmitting ? 'Sending...' : 'Send Message'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
