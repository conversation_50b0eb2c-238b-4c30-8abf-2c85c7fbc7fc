import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ContentType } from '@/types/content';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  locale: string;
  type?: ContentType;
  slug?: string;
  title?: string;
  className?: string;
}

export function Breadcrumb({
  locale,
  type,
  slug,
  title,
  className = '',
}: BreadcrumbProps) {
  const t = useTranslations('breadcrumb');

  const breadcrumbs: BreadcrumbItem[] = [
    { label: t('home'), href: `/${locale}` },
  ];

  if (type) {
    switch (type) {
      case ContentType.PRODUCT:
        breadcrumbs.push({
          label: t('products'),
          href: `/${locale}/products`,
        });
        if (slug && title) {
          breadcrumbs.push({
            label: title,
            href: `/${locale}/products/${slug}`,
            current: true,
          });
        }
        break;
      case ContentType.CASE:
        breadcrumbs.push({
          label: t('cases'),
          href: `/${locale}/cases`,
        });
        if (slug && title) {
          breadcrumbs.push({
            label: title,
            href: `/${locale}/cases/${slug}`,
            current: true,
          });
        }
        break;
      case ContentType.NEWS:
        breadcrumbs.push({
          label: t('news'),
          href: `/${locale}/news`,
        });
        if (slug && title) {
          breadcrumbs.push({
            label: title,
            href: `/${locale}/news/${slug}`,
            current: true,
          });
        }
        break;
      case ContentType.PAGE:
        if (slug && title) {
          breadcrumbs.push({
            label: title,
            href: `/${locale}/${slug}`,
            current: true,
          });
        }
        break;
    }
  }

  return (
    <nav className={`text-sm ${className}`}>
      <ol className='flex items-center space-x-2'>
        {breadcrumbs.map((item, index) => (
          <li key={index} className='flex items-center'>
            {index > 0 && <span className='text-muted-foreground mr-2'>/</span>}
            {item.href && !item.current ? (
              <Link
                href={item.href}
                className='text-muted-foreground hover:text-foreground transition-colors'
              >
                {item.label}
              </Link>
            ) : (
              <span
                className={
                  item.current ? 'font-medium' : 'text-muted-foreground'
                }
              >
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
