'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Languages } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

interface LanguageSwitcherProps {
  currentLocale: string;
}

const languages = [
  { label: '🇬🇧 English', value: 'en' },
  { label: '🇨🇳 中文', value: 'zh' },
];

export function LanguageSwitcher({ currentLocale }: LanguageSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();

  const switchLanguage = (locale: string) => {
    const defaultLocale = 'en';
    let newPathname = pathname;

    // Case 1: If current locale is default and missing from the URL
    if (
      !pathname.startsWith(`/${currentLocale}`) &&
      currentLocale === defaultLocale
    ) {
      // Add the default locale to the beginning to normalize
      newPathname = `/${currentLocale}${pathname}`;
    }

    // Replace current locale with the selected one
    newPathname = newPathname.replace(`/${currentLocale}`, `/${locale}`);

    router.push(newPathname);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='icon'
          className='shrink-0 text-foreground'
        >
          <Languages className='h-[1.2rem] w-[1.2rem]' />
          <span className='sr-only'>Switch language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='min-w-0 w-auto'>
        {languages.map(({ label, value }) => (
          <DropdownMenuItem key={value} onClick={() => switchLanguage(value)}>
            {label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
