import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';
import { type Locale, LOCALES } from '@/lib/constants';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!LOCALES.includes(locale as Locale)) notFound();

  return {
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
