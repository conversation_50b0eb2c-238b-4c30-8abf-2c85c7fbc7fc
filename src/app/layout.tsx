import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

import { ThemeProvider } from '@/components/providers/theme-provider';
import { LOCALES } from '@/lib/constants';
import { cn } from '@/lib/utils';

import './globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
  display: 'swap',
  // Font optimization
  preload: true,
  fallback: ['system-ui', 'arial'],
  adjustFontFallback: true,
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
  display: 'swap',
  // Font optimization
  preload: true,
  fallback: ['ui-monospace', 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', 'monospace'],
  adjustFontFallback: true,
});

export const metadata = {
  title: 'FloodControl - Professional Flood Control Equipment',
  description:
    'Professional flood control equipment and solutions for protecting your property.',
};

type Props = {
  children: ReactNode;
  params?: { locale?: string };
};

export default async function RootLayout({ children, params }: Props) {
  // Extract locale from params or use default
  const locale = params?.locale || 'zh';

  // Validate that the incoming `locale` parameter is valid
  if (!LOCALES.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client side
  const messages = await getMessages();

  return (
    <html
      lang={locale}
      className={cn(
        'font-sans antialiased',
        inter.variable,
        jetbrainsMono.variable
      )}
      suppressHydrationWarning
    >
      <body className='min-h-screen bg-background font-sans antialiased'>
        <ThemeProvider
          attribute='class'
          defaultTheme='system'
          enableSystem
          disableTransitionOnChange
        >
          <NextIntlClientProvider messages={messages}>
            {children}
          </NextIntlClientProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
