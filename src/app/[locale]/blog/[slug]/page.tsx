import { allArticles } from '.contentlayer/generated';
import { Badge } from '@/components/ui/badge';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { ArrowLeft, CalendarDays, Clock, User } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

interface ArticlePageProps {
  params: { locale: string; slug: string };
}

// 标签映射
const tagLabels = {
  zh: {
    '案例分享': '案例分享',
    '行业新闻': '行业新闻',
    '企业动态': '企业动态',
    '灾害新闻': '灾害新闻',
    'case-study': '案例分享',
    'industry-news': '行业新闻',
    'company-news': '企业动态',
    'disaster-news': '灾害新闻',
  },
  en: {
    'case-study': 'Case Studies',
    'industry-news': 'Industry News',
    'company-news': 'Company News',
    'disaster-news': 'Disaster News',
    '案例分享': 'Case Studies',
    '行业新闻': 'Industry News',
    '企业动态': 'Company News',
    '灾害新闻': 'Disaster News',
  },
} as const;

export async function generateStaticParams() {
  return allArticles.map((article) => ({
    locale: article.locale,
    slug: article.slug,
  }));
}

export default async function ArticlePage({
  params: { locale, slug },
}: ArticlePageProps) {
  const t = await getTranslations('pages.blog');
  const breadcrumbT = await getTranslations('breadcrumb');

  const article = allArticles.find(
    (article) => article.slug === slug && article.locale === locale
  );

  if (!article) {
    notFound();
  }

  // 获取相关文章（同标签的其他文章）
  const relatedArticles = allArticles
    .filter(
      (a) =>
        a.locale === locale &&
        a._id !== article._id &&
        a.tags.some((tag) => article.tags.includes(tag))
    )
    .slice(0, 3);

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 面包屑导航 */}
      <Breadcrumb className='mb-6'>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}`}>
              {breadcrumbT('home')}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${locale}/blog`}>
              {locale === 'zh' ? '博客' : 'Blog'}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{article.title}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* 返回按钮 */}
      <div className='mb-6'>
        <Button variant='ghost' asChild>
          <Link href={`/${locale}/blog`} className='flex items-center gap-2'>
            <ArrowLeft className='h-4 w-4' />
            {locale === 'zh' ? '返回博客' : 'Back to Blog'}
          </Link>
        </Button>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
        {/* 主要内容 */}
        <div className='lg:col-span-3'>
          <article>
            {/* 文章头部 */}
            <header className='mb-8'>
              {/* 标签 */}
              <div className='flex flex-wrap gap-2 mb-4'>
                {article.tags.map((tag) => {
                  const localeLabels = tagLabels[locale as keyof typeof tagLabels];
                  const label = (localeLabels as any)[tag] || tag;
                  return (
                    <Badge key={tag} variant='secondary'>
                      {label}
                    </Badge>
                  );
                })}
              </div>

              {/* 标题 */}
              <h1 className='text-4xl font-bold mb-4'>{article.title}</h1>

              {/* 摘要 */}
              <p className='text-xl text-muted-foreground mb-6'>
                {article.excerpt}
              </p>

              {/* 元信息 */}
              <div className='flex flex-wrap items-center gap-6 text-sm text-muted-foreground'>
                {article.author && (
                  <div className='flex items-center gap-2'>
                    <User className='h-4 w-4' />
                    <span>{article.author}</span>
                  </div>
                )}
                <div className='flex items-center gap-2'>
                  <CalendarDays className='h-4 w-4' />
                  <span>
                    {new Date(article.publishDate).toLocaleDateString(
                      locale === 'zh' ? 'zh-CN' : 'en-US'
                    )}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4' />
                  <span>{article.readingTime.text}</span>
                </div>
              </div>

              {/* 特色图片 */}
              {article.featuredImage && (
                <div className='relative h-64 md:h-96 w-full mt-8 rounded-lg overflow-hidden'>
                  <Image
                    src={article.featuredImage}
                    alt={article.title}
                    fill
                    className='object-cover'
                  />
                </div>
              )}
            </header>

            <hr className="mb-8 border-border" />

            {/* 文章内容 */}
            <div className='prose prose-lg max-w-none dark:prose-invert'>
              <div dangerouslySetInnerHTML={{ __html: article.body.html }} />
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className='lg:col-span-1'>
          {/* 相关文章 */}
          {relatedArticles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>
                  {locale === 'zh' ? '相关文章' : 'Related Articles'}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                {relatedArticles.map((relatedArticle) => (
                  <div
                    key={relatedArticle._id}
                    className='border-b pb-4 last:border-b-0 last:pb-0'
                  >
                    <Link
                      href={relatedArticle.url}
                      className='block hover:text-primary'
                    >
                      <h4 className='font-medium line-clamp-2 mb-2'>
                        {relatedArticle.title}
                      </h4>
                      <p className='text-sm text-muted-foreground line-clamp-2 mb-2'>
                        {relatedArticle.excerpt}
                      </p>
                      <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                        <CalendarDays className='h-3 w-3' />
                        <span>
                          {new Date(
                            relatedArticle.publishDate
                          ).toLocaleDateString(
                            locale === 'zh' ? 'zh-CN' : 'en-US'
                          )}
                        </span>
                      </div>
                    </Link>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
