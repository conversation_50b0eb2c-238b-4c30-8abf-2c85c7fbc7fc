import { allArticles } from '.contentlayer/generated';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { CalendarDays, Clock, Search } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';

interface InformationPageProps {
  params: { locale: string };
  searchParams: { tag?: string; search?: string };
}

// 标签映射
const tagMapping = {
  zh: {
    案例分享: 'case-study',
    行业新闻: 'industry-news',
    企业动态: 'company-news',
    灾害新闻: 'disaster-news',
  },
  en: {
    'case-study': '案例分享',
    'industry-news': '行业新闻',
    'company-news': '企业动态',
    'disaster-news': '灾害新闻',
  },
};

const tagLabels = {
  zh: {
    all: '全部',
    案例分享: '案例分享',
    行业新闻: '行业新闻',
    企业动态: '企业动态',
    灾害新闻: '灾害新闻',
  },
  en: {
    all: 'All',
    'case-study': 'Case Studies',
    'industry-news': 'Industry News',
    'company-news': 'Company News',
    'disaster-news': 'Disaster News',
  },
};

export default async function InformationPage({
  params: { locale },
  searchParams,
}: InformationPageProps) {
  const t = await getTranslations('pages.blog');

  // 获取当前语言的文章
  const articles = allArticles
    .filter((article) => article.locale === locale)
    .sort(
      (a, b) =>
        new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime()
    );

  // 过滤文章
  let filteredArticles = articles;

  if (searchParams.tag && searchParams.tag !== 'all') {
    filteredArticles = articles.filter((article) =>
      article.tags.includes(searchParams.tag as any)
    );
  }

  if (searchParams.search) {
    const searchTerm = searchParams.search.toLowerCase();
    filteredArticles = filteredArticles.filter(
      (article) =>
        article.title.toLowerCase().includes(searchTerm) ||
        article.excerpt.toLowerCase().includes(searchTerm)
    );
  }

  const availableTags = [
    'all',
    ...Array.from(new Set(articles.flatMap((article) => article.tags))),
  ];

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 页面标题 */}
      <div className='text-center mb-12'>
        <h1 className='text-4xl font-bold mb-4'>{t('title')}</h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          {t('subtitle')}
        </p>
      </div>

      {/* 搜索和筛选 */}
      <div className='mb-8'>
        <div className='flex flex-col md:flex-row gap-4 mb-6'>
          {/* 搜索框 */}
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
            <form method="GET" className="w-full">
              <Input
                name="search"
                placeholder={
                  locale === 'zh' ? '搜索文章...' : 'Search articles...'
                }
                className='pl-10'
                defaultValue={searchParams.search}
              />
              {searchParams.tag && (
                <input type="hidden" name="tag" value={searchParams.tag} />
              )}
            </form>
          </div>
        </div>

        {/* 标签筛选 */}
        <div className='flex flex-wrap gap-2'>
          {availableTags.map((tag) => {
            const isActive =
              searchParams.tag === tag || (!searchParams.tag && tag === 'all');
            const localeLabels = tagLabels[locale as keyof typeof tagLabels];
            const label = (localeLabels as any)[tag] || tag;

            return (
              <Link
                key={tag}
                href={`/${locale}/blog${tag !== 'all' ? `?tag=${tag}` : ''}`}
              >
                <Badge
                  variant={isActive ? 'default' : 'outline'}
                  className='cursor-pointer hover:bg-primary hover:text-primary-foreground'
                >
                  {label}
                </Badge>
              </Link>
            );
          })}
        </div>
      </div>

      <hr className="mb-8 border-border" />

      {/* 文章列表 */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {filteredArticles.map((article) => (
          <Card
            key={article._id}
            className='overflow-hidden hover:shadow-lg transition-shadow'
          >
            {article.featuredImage && (
              <div className='relative h-48 w-full'>
                <Image
                  src={article.featuredImage}
                  alt={article.title}
                  fill
                  className='object-cover'
                />
              </div>
            )}
            <CardHeader>
              <div className='flex flex-wrap gap-1 mb-2'>
                {article.tags.map((tag) => {
                  const localeLabels = tagLabels[locale as keyof typeof tagLabels];
                  const label = (localeLabels as any)[tag] || tag;
                  return (
                    <Badge key={tag} variant='secondary' className='text-xs'>
                      {label}
                    </Badge>
                  );
                })}
              </div>
              <CardTitle className='line-clamp-2'>
                <Link href={article.url} className='hover:text-primary'>
                  {article.title}
                </Link>
              </CardTitle>
              <CardDescription className='line-clamp-3'>
                {article.excerpt}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex items-center justify-between text-sm text-muted-foreground'>
                <div className='flex items-center gap-2'>
                  <CalendarDays className='h-4 w-4' />
                  <span>
                    {new Date(article.publishDate).toLocaleDateString(
                      locale === 'zh' ? 'zh-CN' : 'en-US'
                    )}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='h-4 w-4' />
                  <span>{article.readingTime.text}</span>
                </div>
              </div>
              <div className='mt-4'>
                <Button asChild variant='outline' size='sm' className='w-full'>
                  <Link href={article.url}>
                    {locale === 'zh' ? '阅读更多' : 'Read More'}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 无结果提示 */}
      {filteredArticles.length === 0 && (
        <div className='text-center py-12'>
          <p className='text-muted-foreground text-lg'>
            {locale === 'zh' ? '暂无相关文章' : 'No articles found'}
          </p>
        </div>
      )}
    </div>
  );
}
