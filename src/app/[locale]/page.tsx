import {
    Hero,
    ModularSystem,
    ProductPortfolio,
    ProfessionalConsultation,
} from '@/components/home';
import { Suspense } from 'react';

interface HomePageProps {
  params: { locale: string };
}

export default function HomePage({ params: { locale } }: HomePageProps) {
  return (
    <>
      {/* Hero Section - 英雄区域，专业定位和核心CTA */}
      <Suspense fallback={<div className='h-screen' />}>
        <Hero locale={locale} />
      </Suspense>

      {/* Product Portfolio - 核心产品矩阵，展示三大产品系列 */}
      <Suspense fallback={<div className='h-96' />}>
        <ProductPortfolio />
      </Suspense>

      {/* Modular System - 模块化防汛系统，三层防护体系 */}
      <Suspense fallback={<div className='h-96' />}>
        <ModularSystem />
      </Suspense>

      {/* Professional Consultation - 全球合作伙伴计划，共同守护防汛安全 */}
      <Suspense fallback={<div className='h-96' />}>
        <ProfessionalConsultation />
      </Suspense>
    </>
  );
}
