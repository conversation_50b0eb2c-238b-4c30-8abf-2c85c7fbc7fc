import { useTranslations } from 'next-intl';

interface PrivacyPageProps {
  params: { locale: string };
}

export default function PrivacyPage({ params: { locale } }: PrivacyPageProps) {
  const t = useTranslations('web.legal.privacy');

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto max-w-4xl'>
        <div className='flex flex-col gap-8'>
          <div className='flex flex-col gap-4'>
            <h1 className='text-4xl font-bold tracking-tight md:text-5xl'>
              Privacy Policy
            </h1>
            <p className='text-xl text-muted-foreground'>
              Last updated: {new Date().toLocaleDateString(locale)}
            </p>
          </div>

          <div className='prose prose-lg max-w-none dark:prose-invert'>
            <h2>Information We Collect</h2>
            <p>
              We collect information you provide directly to us, such as when
              you create an account, make a purchase, or contact us for support.
            </p>

            <h2>How We Use Your Information</h2>
            <p>
              We use the information we collect to provide, maintain, and
              improve our services, process transactions, and communicate with
              you.
            </p>

            <h2>Information Sharing</h2>
            <p>
              We do not sell, trade, or otherwise transfer your personal
              information to third parties without your consent, except as
              described in this policy.
            </p>

            <h2>Data Security</h2>
            <p>
              We implement appropriate security measures to protect your
              personal information against unauthorized access, alteration,
              disclosure, or destruction.
            </p>

            <h2>Contact Us</h2>
            <p>
              If you have any questions about this Privacy Policy, please
              contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
