import { useTranslations } from 'next-intl';

interface TermsPageProps {
  params: { locale: string };
}

export default function TermsPage({ params: { locale } }: TermsPageProps) {
  const t = useTranslations('web.legal.terms');

  return (
    <div className='w-full py-20 lg:py-40'>
      <div className='container mx-auto max-w-4xl'>
        <div className='flex flex-col gap-8'>
          <div className='flex flex-col gap-4'>
            <h1 className='text-4xl font-bold tracking-tight md:text-5xl'>
              Terms of Service
            </h1>
            <p className='text-xl text-muted-foreground'>
              Last updated: {new Date().toLocaleDateString(locale)}
            </p>
          </div>

          <div className='prose prose-lg max-w-none dark:prose-invert'>
            <h2>Acceptance of Terms</h2>
            <p>
              By accessing and using our services, you accept and agree to be
              bound by the terms and provision of this agreement.
            </p>

            <h2>Use License</h2>
            <p>
              Permission is granted to temporarily download one copy of the
              materials on our website for personal, non-commercial transitory
              viewing only.
            </p>

            <h2>Disclaimer</h2>
            <p>
              The materials on our website are provided on an 'as is' basis. We
              make no warranties, expressed or implied, and hereby disclaim and
              negate all other warranties.
            </p>

            <h2>Limitations</h2>
            <p>
              In no event shall our company or its suppliers be liable for any
              damages arising out of the use or inability to use the materials
              on our website.
            </p>

            <h2>Accuracy of Materials</h2>
            <p>
              The materials appearing on our website could include technical,
              typographical, or photographic errors. We do not warrant that any
              of the materials are accurate, complete, or current.
            </p>

            <h2>Contact Information</h2>
            <p>
              If you have any questions about these Terms of Service, please
              contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
