import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Award,
    Building2,
    CheckCircle,
    Globe,
    Heart,
    HelpCircle,
    Mail,
    MapPin,
    Phone,
    Target,
    Users,
} from 'lucide-react';

interface AboutPageProps {
  params: { locale: string };
}

export default function AboutPage({ params: { locale } }: AboutPageProps) {
  const navigationItems = [
    { id: 'company', label: locale === 'zh' ? '企业简介' : 'Company', icon: Building2 },
    { id: 'vision', label: locale === 'zh' ? '企业愿景' : 'Vision', icon: Target },
    { id: 'values', label: locale === 'zh' ? '企业价值观' : 'Values', icon: Heart },
    { id: 'faq', label: locale === 'zh' ? '常见问题' : 'FAQ', icon: HelpCircle },
    { id: 'contact', label: locale === 'zh' ? '联系我们' : 'Contact', icon: Mail },
  ];

  return (
    <div className="about-page">
      {/* 页面内导航 */}
      <nav className="sticky top-20 bg-white/80 backdrop-blur-sm border-b z-30">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8 py-4 overflow-x-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <a
                  key={item.id}
                  href={`#${item.id}`}
                  className="flex items-center space-x-2 hover:text-primary whitespace-nowrap transition-colors"
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </a>
              );
            })}
          </div>
        </div>
      </nav>

      {/* 页面标题 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold mb-4">
            {locale === 'zh' ? '关于我们' : 'About Us'}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {locale === 'zh'
              ? '专业的防汛设备制造商，致力于为全球客户提供高质量的防洪解决方案'
              : 'Professional flood control equipment manufacturer, committed to providing high-quality flood protection solutions for global customers'
            }
          </p>
        </div>
      </div>

      {/* 企业简介区域 */}
      <section id="company" className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Building2 className="h-12 w-12 text-primary mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {locale === 'zh' ? '企业简介' : 'Company Overview'}
              </h2>
            </div>

            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <p className="text-lg text-muted-foreground mb-6">
                  {locale === 'zh'
                    ? '我们是一家专业从事防汛设备研发、生产和销售的高新技术企业。公司成立于2003年，拥有20年的行业经验，致力于为全球客户提供专业的防洪解决方案。'
                    : 'We are a high-tech enterprise specializing in the research, development, production and sales of flood control equipment. Founded in 2003, we have 20 years of industry experience and are committed to providing professional flood protection solutions for global customers.'
                  }
                </p>

                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">20+</div>
                      <div className="text-sm text-muted-foreground">
                        {locale === 'zh' ? '年行业经验' : 'Years Experience'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">500+</div>
                      <div className="text-sm text-muted-foreground">
                        {locale === 'zh' ? '成功项目' : 'Successful Projects'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">50+</div>
                      <div className="text-sm text-muted-foreground">
                        {locale === 'zh' ? '专业团队' : 'Team Members'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">15+</div>
                      <div className="text-sm text-muted-foreground">
                        {locale === 'zh' ? '服务国家' : 'Countries Served'}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '技术领先' : 'Technology Leadership'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '拥有多项专利技术和创新产品' : 'Multiple patented technologies and innovative products'}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Users className="h-5 w-5 text-blue-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '专业团队' : 'Professional Team'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '经验丰富的工程师和技术专家' : 'Experienced engineers and technical experts'}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Award className="h-5 w-5 text-yellow-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '质量认证' : 'Quality Certification'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '通过ISO9001等多项国际认证' : 'ISO9001 and multiple international certifications'}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Globe className="h-5 w-5 text-purple-500 mt-1" />
                  <div>
                    <h4 className="font-semibold">
                      {locale === 'zh' ? '全球服务' : 'Global Service'}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {locale === 'zh' ? '为全球客户提供专业服务' : 'Professional services for global customers'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <hr className="my-8 border-border" />

      {/* 企业愿景区域 */}
      <section id="vision" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Target className="h-12 w-12 text-primary mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {locale === 'zh' ? '企业愿景' : 'Our Vision'}
              </h2>
              <p className="text-lg text-muted-foreground">
                {locale === 'zh'
                  ? '成为全球领先的防汛设备制造商，为人类防灾减灾事业贡献力量'
                  : 'To become a leading global manufacturer of flood control equipment and contribute to human disaster prevention and mitigation'
                }
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-bold">1</span>
                    </div>
                    <span>{locale === 'zh' ? '技术创新' : 'Innovation'}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '持续投入研发，推动防汛技术创新，为客户提供更先进的解决方案'
                      : 'Continuous R&D investment to drive flood control technology innovation and provide advanced solutions'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-bold">2</span>
                    </div>
                    <span>{locale === 'zh' ? '全球服务' : 'Global Service'}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '建立全球服务网络，为世界各地的客户提供及时专业的技术支持'
                      : 'Build a global service network to provide timely and professional technical support worldwide'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-bold">3</span>
                    </div>
                    <span>{locale === 'zh' ? '可持续发展' : 'Sustainability'}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '坚持绿色环保理念，开发可持续的防汛解决方案，保护生态环境'
                      : 'Adhere to green environmental protection concepts and develop sustainable flood control solutions'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-bold">4</span>
                    </div>
                    <span>{locale === 'zh' ? '社会责任' : 'Social Responsibility'}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '积极履行社会责任，参与公益防汛项目，为社会安全贡献力量'
                      : 'Actively fulfill social responsibilities and participate in public welfare flood control projects'
                    }
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <hr className="my-8 border-border" />

      {/* 企业价值观区域 */}
      <section id="values" className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Heart className="h-12 w-12 text-primary mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {locale === 'zh' ? '企业价值观' : 'Our Values'}
              </h2>
              <p className="text-lg text-muted-foreground">
                {locale === 'zh'
                  ? '我们的核心价值观指导着我们的行为和决策，塑造着我们的企业文化'
                  : 'Our core values guide our behavior and decisions, shaping our corporate culture'
                }
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <Card className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {locale === 'zh' ? '客户至上' : 'Customer First'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '始终以客户需求为导向，提供超越期望的产品和服务'
                      : 'Always customer-oriented, providing products and services that exceed expectations'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {locale === 'zh' ? '诚信经营' : 'Integrity'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '坚持诚实守信，建立长期稳定的合作关系'
                      : 'Adhere to honesty and trustworthiness, building long-term stable partnerships'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {locale === 'zh' ? '持续创新' : 'Continuous Innovation'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '不断追求技术创新和产品改进，保持行业领先地位'
                      : 'Continuously pursue technological innovation and product improvement to maintain industry leadership'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {locale === 'zh' ? '团队合作' : 'Teamwork'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    {locale === 'zh'
                      ? '倡导团队协作精神，共同实现企业目标和个人成长'
                      : 'Advocate team collaboration spirit to achieve corporate goals and personal growth together'
                    }
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <hr className="my-8 border-border" />

      {/* 常见问题区域 */}
      <section id="faq" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <HelpCircle className="h-12 w-12 text-primary mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {locale === 'zh' ? '常见问题' : 'Frequently Asked Questions'}
              </h2>
            </div>

            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem value="item-1" className="bg-white rounded-lg px-6">
                <AccordionTrigger className="text-left hover:no-underline">
                  {locale === 'zh' ? '你们的产品质量如何保证？' : 'How do you ensure product quality?'}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {locale === 'zh'
                    ? '我们通过了ISO9001质量管理体系认证，拥有完善的质量控制流程。每个产品都经过严格的测试和检验，确保符合国际标准。'
                    : 'We are ISO9001 certified with comprehensive quality control processes. Every product undergoes rigorous testing and inspection to ensure compliance with international standards.'
                  }
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2" className="bg-white rounded-lg px-6">
                <AccordionTrigger className="text-left hover:no-underline">
                  {locale === 'zh' ? '产品的交货期是多长？' : 'What is the delivery time for products?'}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {locale === 'zh'
                    ? '标准产品的交货期通常为2-4周，定制产品根据复杂程度需要4-8周。我们会根据客户的紧急程度调整生产计划。'
                    : 'Standard products typically have a 2-4 week delivery time, while custom products require 4-8 weeks depending on complexity. We adjust production schedules based on customer urgency.'
                  }
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3" className="bg-white rounded-lg px-6">
                <AccordionTrigger className="text-left hover:no-underline">
                  {locale === 'zh' ? '是否提供技术培训和支持？' : 'Do you provide technical training and support?'}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {locale === 'zh'
                    ? '是的，我们为客户提供全面的技术培训，包括产品安装、操作和维护。同时提供24小时技术支持热线和远程技术指导。'
                    : 'Yes, we provide comprehensive technical training including product installation, operation, and maintenance. We also offer 24-hour technical support hotline and remote technical guidance.'
                  }
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4" className="bg-white rounded-lg px-6">
                <AccordionTrigger className="text-left hover:no-underline">
                  {locale === 'zh' ? '如何成为你们的代理商？' : 'How to become your distributor?'}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {locale === 'zh'
                    ? '我们欢迎有实力的合作伙伴加入我们的代理网络。请联系我们的销售团队，我们会根据您的市场情况和能力评估合作可能性。'
                    : 'We welcome capable partners to join our distributor network. Please contact our sales team, and we will evaluate cooperation possibilities based on your market situation and capabilities.'
                  }
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5" className="bg-white rounded-lg px-6">
                <AccordionTrigger className="text-left hover:no-underline">
                  {locale === 'zh' ? '产品保修期是多长？' : 'What is the product warranty period?'}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  {locale === 'zh'
                    ? '我们的产品提供2年质保期，在正常使用条件下如出现质量问题，我们将免费维修或更换。同时提供终身技术支持服务。'
                    : 'Our products come with a 2-year warranty. Under normal use conditions, we will repair or replace products free of charge if quality issues arise. We also provide lifetime technical support services.'
                  }
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      <hr className="my-8 border-border" />

      {/* 联系我们区域 */}
      <section id="contact" className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <Mail className="h-12 w-12 text-primary mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                {locale === 'zh' ? '联系我们' : 'Contact Us'}
              </h2>
              <p className="text-lg text-muted-foreground">
                {locale === 'zh' ? '联系我们获取专业的防汛解决方案' : 'Contact us for professional flood control solutions'}
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* 联系信息 */}
              <div className="space-y-8">
                <div>
                  <h3 className="text-xl font-semibold mb-6">
                    {locale === 'zh' ? '联系方式' : 'Contact Information'}
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">{locale === 'zh' ? '邮箱' : 'Email'}</div>
                        <div className="text-muted-foreground"><EMAIL></div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">{locale === 'zh' ? '电话' : 'Phone'}</div>
                        <div className="text-muted-foreground">+86 ************</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">{locale === 'zh' ? '地址' : 'Address'}</div>
                        <div className="text-muted-foreground">
                          {locale === 'zh' ? '中国上海市浦东新区科技园区123号' : '123 Tech Park, Pudong, Shanghai, China'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4">
                    {locale === 'zh' ? '工作时间' : 'Business Hours'}
                  </h3>
                  <div className="space-y-2 text-muted-foreground">
                    <div>{locale === 'zh' ? '周一至周五：9:00 - 18:00' : 'Monday - Friday: 9:00 - 18:00'}</div>
                    <div>{locale === 'zh' ? '周六：9:00 - 12:00' : 'Saturday: 9:00 - 12:00'}</div>
                    <div>{locale === 'zh' ? '周日：休息' : 'Sunday: Closed'}</div>
                  </div>
                </div>
              </div>

              {/* 联系表单 */}
              <Card>
                <CardHeader>
                  <CardTitle>{locale === 'zh' ? '发送消息' : 'Send Message'}</CardTitle>
                  <CardDescription>
                    {locale === 'zh' ? '填写表单，我们会尽快回复您' : 'Fill out the form and we will get back to you soon'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">{locale === 'zh' ? '名字' : 'First Name'}</Label>
                      <Input id="firstName" placeholder={locale === 'zh' ? '请输入您的名字' : 'Enter your first name'} />
                    </div>
                    <div>
                      <Label htmlFor="lastName">{locale === 'zh' ? '姓氏' : 'Last Name'}</Label>
                      <Input id="lastName" placeholder={locale === 'zh' ? '请输入您的姓氏' : 'Enter your last name'} />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email">{locale === 'zh' ? '邮箱' : 'Email'}</Label>
                    <Input id="email" type="email" placeholder={locale === 'zh' ? '请输入您的邮箱' : 'Enter your email'} />
                  </div>
                  <div>
                    <Label htmlFor="company">{locale === 'zh' ? '公司（可选）' : 'Company (Optional)'}</Label>
                    <Input id="company" placeholder={locale === 'zh' ? '请输入您的公司名称' : 'Enter your company name'} />
                  </div>
                  <div>
                    <Label htmlFor="message">{locale === 'zh' ? '留言' : 'Message'}</Label>
                    <Textarea
                      id="message"
                      placeholder={locale === 'zh' ? '请详细描述您的需求...' : 'Please describe your requirements in detail...'}
                      rows={4}
                    />
                  </div>
                  <Button className="w-full">
                    {locale === 'zh' ? '发送消息' : 'Send Message'}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
