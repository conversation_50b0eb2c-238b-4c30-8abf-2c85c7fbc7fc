import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { LOCALES } from '@/lib/constants';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: { locale: string };
}

export default async function LocaleLayout({
  children,
  params: { locale },
}: LocaleLayoutProps) {
  // Validate that the incoming `locale` parameter is valid
  if (!LOCALES.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <div className='relative flex min-h-screen flex-col'>
        <Header locale={locale} />
        <main className='flex-1'>{children}</main>
        <Footer locale={locale} />
      </div>
    </NextIntlClientProvider>
  );
}
