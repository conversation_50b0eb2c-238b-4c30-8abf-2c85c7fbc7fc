import { getAllProducts } from '@/lib/content';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

interface ProductsPageProps {
  params: {
    locale: string;
  };
}

export default function ProductsPage({ params }: ProductsPageProps) {
  const t = useTranslations('pages.products');
  const products = getAllProducts(params.locale);

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <h1 className='text-4xl font-bold mb-4'>{t('title')}</h1>
        <p className='text-lg text-muted-foreground'>{t('subtitle')}</p>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {products.map((product) => (
          <Link key={product._id} href={product.url}>
            <Card className='h-full hover:shadow-lg transition-shadow'>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <CardTitle className='text-xl'>{product.title}</CardTitle>
                  {product.featured && (
                    <Badge variant='secondary'>{t('featured')}</Badge>
                  )}
                </div>
                <CardDescription>{product.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between text-sm'>
                    <span className='text-muted-foreground'>
                      {t('category')}:
                    </span>
                    <Badge variant='outline'>{product.category}</Badge>
                  </div>
                  <div className='flex items-center justify-between text-sm'>
                    <span className='text-muted-foreground'>
                      {t('publishDate')}:
                    </span>
                    <span>{new Date(product.date).toLocaleDateString()}</span>
                  </div>
                  {product.readingTime && (
                    <div className='flex items-center justify-between text-sm'>
                      <span className='text-muted-foreground'>
                        {t('readingTime')}:
                      </span>
                      <span>{product.readingTime.text}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {products.length === 0 && (
        <div className='text-center py-12'>
          <p className='text-lg text-muted-foreground'>{t('noProducts')}</p>
        </div>
      )}
    </div>
  );
}
