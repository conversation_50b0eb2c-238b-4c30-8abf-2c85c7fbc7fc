import { getProductBySlug, getAllProducts } from '@/lib/content';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Breadcrumb } from '@/components/shared/breadcrumb';
import { ContentType } from '@/types/content';
import { useTranslations } from 'next-intl';
import { notFound } from 'next/navigation';

interface ProductPageProps {
  params: {
    locale: string;
    slug: string;
  };
}

export default function ProductPage({ params }: ProductPageProps) {
  const t = useTranslations('pages.products');
  const tCommon = useTranslations('common');
  const product = getProductBySlug(params.slug, params.locale);

  if (!product) {
    notFound();
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 面包屑导航 */}
      <Breadcrumb
        locale={params.locale}
        type={ContentType.PRODUCT}
        slug={params.slug}
        title={product.title}
        className='mb-8'
      />

      {/* 产品标题和基本信息 */}
      <div className='mb-8'>
        <div className='flex items-center gap-4 mb-4'>
          <h1 className='text-4xl font-bold'>{product.title}</h1>
          {product.featured && (
            <Badge variant='secondary' className='text-sm'>
              {t('featuredProduct')}
            </Badge>
          )}
        </div>
        <p className='text-xl text-muted-foreground mb-4'>
          {product.description}
        </p>
        <div className='flex items-center gap-4 text-sm text-muted-foreground'>
          <span>
            {t('category')}:
            <Badge variant='outline' className='ml-2'>
              {product.category}
            </Badge>
          </span>
          <span>
            {tCommon('published')}:{' '}
            {new Date(product.date).toLocaleDateString()}
          </span>
          {product.readingTime && (
            <span>
              {t('readingTime')}: {product.readingTime.text}
            </span>
          )}
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        {/* 主要内容 */}
        <div className='lg:col-span-2'>
          <div className='prose prose-lg max-w-none'>
            <div dangerouslySetInnerHTML={{ __html: product.body.html }} />
          </div>
        </div>

        {/* 侧边栏 */}
        <div className='space-y-6'>
          {/* 技术规格 */}
          {product.specifications && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>{t('specifications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className='space-y-2'>
                  {Object.entries(product.specifications).map(
                    ([key, value]) => (
                      <div key={key} className='flex justify-between'>
                        <dt className='text-sm text-muted-foreground capitalize'>
                          {key}:
                        </dt>
                        <dd className='text-sm font-medium'>{String(value)}</dd>
                      </div>
                    )
                  )}
                </dl>
              </CardContent>
            </Card>
          )}

          {/* 应用场景 */}
          {product.applications && product.applications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>{t('applications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className='space-y-2'>
                  {product.applications.map((application, index) => (
                    <li key={index} className='text-sm flex items-center'>
                      <span className='w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0'></span>
                      {application}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* 认证信息 */}
          {product.certifications && product.certifications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>{t('certifications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='flex flex-wrap gap-2'>
                  {product.certifications.map((cert, index) => (
                    <Badge key={index} variant='outline'>
                      {cert}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

// 生成静态路径
export async function generateStaticParams({
  params,
}: {
  params: { locale: string };
}) {
  const products = getAllProducts(params.locale);

  return products.map((product) => ({
    slug: product.slug,
  }));
}
