// 导出 Contentlayer 生成的类型
export type { Article, Page, Product } from '.contentlayer/generated';

// 内容相关的通用类型
export interface ContentMeta {
  title: string;
  description: string;
  date: string;
  locale: string;
  slug: string;
  url: string;
  readingTime: {
    text: string;
    minutes: number;
    time: number;
    words: number;
  };
}

// 产品相关类型
export interface ProductSpecifications {
  height?: string;
  material?: string;
  deployment?: string;
  certification?: string;
  [key: string]: string | undefined;
}

export interface ProductFilters {
  category?: string;
  featured?: boolean;
  locale?: string;
}

// 案例相关类型
export interface CaseFilters {
  location?: string;
  client?: string;
  featured?: boolean;
  locale?: string;
}

// 新闻相关类型
export interface NewsFilters {
  category?: string;
  author?: string;
  tags?: string[];
  featured?: boolean;
  locale?: string;
}

// 页面相关类型
export interface PageSections {
  hero?: {
    title: string;
    subtitle?: string;
    image?: string;
  };
  stats?: Array<{
    label: string;
    value: string;
  }>;
  features?: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
  [key: string]: any;
}

// 内容查询参数
export interface ContentQueryParams {
  locale?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'date' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// 内容搜索参数
export interface ContentSearchParams extends ContentQueryParams {
  query?: string;
  category?: string;
  tags?: string[];
  featured?: boolean;
}

// 分页结果
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 内容统计
export interface ContentStats {
  totalProducts: number;
  totalCases: number;
  totalNews: number;
  totalPages: number;
  featuredProducts: number;
  featuredCases: number;
  featuredNews: number;
}

// 相关内容
export interface RelatedContent<T> {
  current: T;
  related: T[];
  prev?: T;
  next?: T;
}

// 内容导航
export interface ContentNavigation {
  label: string;
  href: string;
  count?: number;
  children?: ContentNavigation[];
}

// SEO 元数据
export interface SEOMetadata {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url: string;
  type: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  locale: string;
  alternateLocales?: string[];
}

// 面包屑导航
export interface Breadcrumb {
  label: string;
  href?: string;
  current?: boolean;
}

// 内容状态
export type ContentStatus = 'draft' | 'published' | 'archived';

// 内容类型枚举
export enum ContentType {
  PRODUCT = 'product',
  CASE = 'case',
  NEWS = 'news',
  PAGE = 'page',
}

// 语言枚举
export enum Locale {
  ZH = 'zh',
  EN = 'en',
}

// 内容排序选项
export enum SortOption {
  DATE_DESC = 'date-desc',
  DATE_ASC = 'date-asc',
  TITLE_ASC = 'title-asc',
  TITLE_DESC = 'title-desc',
  FEATURED = 'featured',
}

// 内容过滤器
export interface ContentFilter {
  type?: ContentType;
  locale?: Locale;
  category?: string;
  featured?: boolean;
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// 内容聚合结果
export interface ContentAggregation {
  products: {
    total: number;
    featured: number;
    categories: Record<string, number>;
  };
  cases: {
    total: number;
    featured: number;
    locations: Record<string, number>;
  };
  news: {
    total: number;
    featured: number;
    categories: Record<string, number>;
    tags: Record<string, number>;
  };
  pages: {
    total: number;
  };
}

// 内容验证错误
export interface ContentValidationError {
  field: string;
  message: string;
  code: string;
}

// 内容操作结果
export interface ContentOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: ContentValidationError[];
}
