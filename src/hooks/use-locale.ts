'use client';

import { useParams } from 'next/navigation';
import { type Locale, DEFAULT_LOCALE } from '@/lib/constants';

/**
 * Hook to get current locale from URL params
 */
export function useLocale(): Locale {
  const params = useParams();
  const locale = params?.locale as string;

  // Validate locale and return default if invalid
  if (locale && ['en', 'zh'].includes(locale)) {
    return locale as Locale;
  }

  return DEFAULT_LOCALE;
}
