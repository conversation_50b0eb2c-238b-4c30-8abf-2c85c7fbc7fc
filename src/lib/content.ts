import type {
    Article,
    Page,
    Product
} from '.contentlayer/generated';
import {
    allArticles,
    allPages,
    allProducts
} from '.contentlayer/generated';

/**
 * 获取所有产品
 */
export function getAllProducts(locale?: string): Product[] {
  return locale
    ? allProducts.filter((product) => product.locale === locale)
    : allProducts;
}

/**
 * 获取所有文章
 */
export function getAllArticles(locale?: string): Article[] {
  return locale
    ? allArticles.filter((article) => article.locale === locale)
    : allArticles;
}

/**
 * 获取所有页面
 */
export function getAllPages(locale?: string): Page[] {
  return locale ? allPages.filter((page) => page.locale === locale) : allPages;
}

/**
 * 根据 slug 获取产品
 */
export function getProductBySlug(
  slug: string,
  locale: string
): Product | undefined {
  return allProducts.find(
    (product) => product.slug === slug && product.locale === locale
  );
}

/**
 * 根据 slug 获取文章
 */
export function getArticleBySlug(slug: string, locale: string): Article | undefined {
  return allArticles.find(
    (article) => article.slug === slug && article.locale === locale
  );
}

/**
 * 根据 slug 获取页面
 */
export function getPageBySlug(slug: string, locale: string): Page | undefined {
  return allPages.find((page) => page.slug === slug && page.locale === locale);
}

/**
 * 获取特色产品
 */
export function getFeaturedProducts(
  locale?: string,
  limit?: number
): Product[] {
  const products = getAllProducts(locale).filter((product) => product.featured);
  return limit ? products.slice(0, limit) : products;
}

/**
 * 获取特色文章
 */
export function getFeaturedArticles(locale?: string, limit?: number): Article[] {
  const articles = getAllArticles(locale).filter((article) => article.featured);
  return limit ? articles.slice(0, limit) : articles;
}

/**
 * 按分类获取产品
 */
export function getProductsByCategory(
  category: string,
  locale?: string
): Product[] {
  return getAllProducts(locale).filter(
    (product) => product.category === category
  );
}
