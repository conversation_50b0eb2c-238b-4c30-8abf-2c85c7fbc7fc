// Supported locales
export const LOCALES = ['en', 'zh'] as const;
export type Locale = (typeof LOCALES)[number];

// Default locale
export const DEFAULT_LOCALE: Locale = 'en';

// Site configuration
export const SITE_CONFIG = {
  name: 'FloodControl Equipment',
  description: 'Professional flood control equipment and solutions',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  ogImage: '/og-image.jpg',
  links: {
    twitter: 'https://twitter.com/floodcontrol',
    github: 'https://github.com/floodcontrol',
  },
} as const;

// Navigation items
export const NAVIGATION_ITEMS = [
  { href: '/', label: 'Home' },
  { href: '/products', label: 'Products' },
  { href: '/cases', label: 'Cases' },
  { href: '/about', label: 'About' },
  { href: '/contact', label: 'Contact' },
] as const;

// Product categories
export const PRODUCT_CATEGORIES = [
  'flood-barriers',
  'pumping-systems',
  'drainage-solutions',
  'monitoring-equipment',
] as const;

export type ProductCategory = (typeof PRODUCT_CATEGORIES)[number];
