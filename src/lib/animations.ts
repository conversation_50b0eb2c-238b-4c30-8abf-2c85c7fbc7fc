/**
 * Animation configurations and utilities for Framer Motion
 * Provides consistent animation variants and transitions across the application
 */

import { Variants, Transition } from 'framer-motion';

// Common transition configurations
export const transitions = {
  // Default smooth transition
  default: {
    type: 'spring',
    stiffness: 100,
    damping: 15,
    mass: 1,
  } as Transition,

  // Quick transition for hover effects
  quick: {
    type: 'spring',
    stiffness: 400,
    damping: 25,
    mass: 0.5,
  } as Transition,

  // Smooth transition for page transitions
  smooth: {
    type: 'spring',
    stiffness: 80,
    damping: 20,
    mass: 1,
  } as Transition,

  // Bouncy transition for attention-grabbing elements
  bouncy: {
    type: 'spring',
    stiffness: 300,
    damping: 10,
    mass: 0.8,
  } as Transition,

  // Linear transition for consistent timing
  linear: {
    duration: 0.3,
    ease: 'easeInOut',
  } as Transition,
};

// Fade animation variants
export const fadeVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: transitions.default,
  },
  exit: {
    opacity: 0,
    transition: transitions.quick,
  },
};

// Slide animation variants
export const slideVariants = {
  // Slide from left
  fromLeft: {
    hidden: {
      opacity: 0,
      x: -50,
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      x: -50,
      transition: transitions.quick,
    },
  } as Variants,

  // Slide from right
  fromRight: {
    hidden: {
      opacity: 0,
      x: 50,
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      x: 50,
      transition: transitions.quick,
    },
  } as Variants,

  // Slide from top
  fromTop: {
    hidden: {
      opacity: 0,
      y: -50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      y: -50,
      transition: transitions.quick,
    },
  } as Variants,

  // Slide from bottom
  fromBottom: {
    hidden: {
      opacity: 0,
      y: 50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      y: 50,
      transition: transitions.quick,
    },
  } as Variants,
};

// Scale animation variants
export const scaleVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: transitions.bouncy,
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: transitions.quick,
  },
};

// Hover animation variants
export const hoverVariants: Variants = {
  rest: {
    scale: 1,
  },
  hover: {
    scale: 1.05,
    transition: transitions.quick,
  },
  tap: {
    scale: 0.95,
    transition: transitions.quick,
  },
};

// Stagger animation for lists
export const staggerContainer: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

export const staggerItem: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: transitions.smooth,
  },
};

// Page transition variants
export const pageVariants: Variants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
    transition: transitions.smooth,
  },
  out: {
    opacity: 0,
    y: -20,
    transition: transitions.quick,
  },
};

// Card animation variants
export const cardVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: transitions.smooth,
  },
  hover: {
    y: -5,
    scale: 1.02,
    transition: transitions.quick,
  },
};

// Hero section animation variants
export const heroVariants = {
  title: {
    hidden: {
      opacity: 0,
      y: 30,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        ...transitions.smooth,
        delay: 0.2,
      },
    },
  } as Variants,

  subtitle: {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        ...transitions.smooth,
        delay: 0.4,
      },
    },
  } as Variants,

  cta: {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        ...transitions.smooth,
        delay: 0.6,
      },
    },
  } as Variants,
};

// Utility function to create custom slide variants
export const createSlideVariants = (
  direction: 'left' | 'right' | 'up' | 'down',
  distance: number = 50
): Variants => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'left':
        return { x: -distance, y: 0 };
      case 'right':
        return { x: distance, y: 0 };
      case 'up':
        return { x: 0, y: -distance };
      case 'down':
        return { x: 0, y: distance };
      default:
        return { x: 0, y: 0 };
    }
  };

  const initialPos = getInitialPosition();

  return {
    hidden: {
      opacity: 0,
      ...initialPos,
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: transitions.smooth,
    },
    exit: {
      opacity: 0,
      ...initialPos,
      transition: transitions.quick,
    },
  };
};

// Utility function to create stagger animation with custom delay
export const createStaggerVariants = (
  staggerDelay: number = 0.1,
  childrenDelay: number = 0.2
): Variants => ({
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: staggerDelay,
      delayChildren: childrenDelay,
    },
  },
});
