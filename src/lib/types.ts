import { type Locale, type ProductCategory } from './constants';

// Base types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Product types
export interface Product extends BaseEntity {
  title: string;
  description: string;
  category: ProductCategory;
  images: string[];
  specifications: Record<string, string>;
  price?: number;
  slug: string;
  locale: Locale;
  featured: boolean;
}

// Case study types
export interface CaseStudy extends BaseEntity {
  title: string;
  description: string;
  content: string;
  images: string[];
  client: string;
  location: string;
  completedAt: Date;
  tags: string[];
  slug: string;
  locale: Locale;
  featured: boolean;
}

// News/Blog types
export interface NewsArticle extends BaseEntity {
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: Date;
  tags: string[];
  slug: string;
  locale: Locale;
  featured: boolean;
  coverImage?: string;
}

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  company?: string;
  phone?: string;
  subject: string;
  message: string;
  locale: Locale;
}

// Navigation types
export interface NavigationItem {
  href: string;
  label: string;
  children?: NavigationItem[];
}

// SEO types
export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonical?: string;
}
