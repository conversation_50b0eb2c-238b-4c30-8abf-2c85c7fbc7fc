#!/usr/bin/env node

/**
 * SEO优化验证脚本
 * 检查页面的SEO元素和可访问性
 */

const puppeteer = require('puppeteer');

async function testSEOOptimization() {
  console.log('🔍 启动SEO优化验证...\n');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const page = await browser.newPage();

    // 加载页面
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 15000,
    });

    // 获取页面SEO信息
    const seoData = await page.evaluate(() => {
      const data = {};

      // 基础元数据
      data.title = document.title;
      data.metaDescription = document.querySelector(
        'meta[name="description"]'
      )?.content;
      data.metaKeywords = document.querySelector(
        'meta[name="keywords"]'
      )?.content;
      data.lang = document.documentElement.lang;

      // Open Graph
      data.ogTitle = document.querySelector(
        'meta[property="og:title"]'
      )?.content;
      data.ogDescription = document.querySelector(
        'meta[property="og:description"]'
      )?.content;
      data.ogType = document.querySelector('meta[property="og:type"]')?.content;
      data.ogUrl = document.querySelector('meta[property="og:url"]')?.content;

      // Twitter Cards
      data.twitterCard = document.querySelector(
        'meta[name="twitter:card"]'
      )?.content;
      data.twitterTitle = document.querySelector(
        'meta[name="twitter:title"]'
      )?.content;
      data.twitterDescription = document.querySelector(
        'meta[name="twitter:description"]'
      )?.content;

      // 结构化数据
      data.jsonLd = Array.from(
        document.querySelectorAll('script[type="application/ld+json"]')
      ).map((script) => script.textContent);

      // 标题结构
      data.headings = {
        h1: Array.from(document.querySelectorAll('h1')).map((h) =>
          h.textContent.trim()
        ),
        h2: Array.from(document.querySelectorAll('h2')).map((h) =>
          h.textContent.trim()
        ),
        h3: Array.from(document.querySelectorAll('h3')).map((h) =>
          h.textContent.trim()
        ),
      };

      // 图片优化
      data.images = Array.from(document.querySelectorAll('img')).map((img) => ({
        src: img.src,
        alt: img.alt,
        loading: img.loading,
        hasAlt: !!img.alt,
        hasLazyLoading: img.loading === 'lazy',
      }));

      // 链接
      data.links = Array.from(document.querySelectorAll('a')).map((link) => ({
        href: link.href,
        text: link.textContent.trim(),
        hasTitle: !!link.title,
        isExternal: link.hostname !== window.location.hostname,
        hasNoopener: link.rel.includes('noopener'),
      }));

      // 可访问性
      data.accessibility = {
        hasSkipLink: !!document.querySelector(
          'a[href="#main"], a[href="#content"]'
        ),
        mainLandmark: !!document.querySelector('main'),
        navLandmark: !!document.querySelector('nav'),
        ariaLabels: document.querySelectorAll('[aria-label]').length,
        ariaDescribedBy: document.querySelectorAll('[aria-describedby]').length,
      };

      return data;
    });

    // 验证结果
    const checks = {
      title: {
        exists: !!seoData.title,
        length: seoData.title?.length || 0,
        optimal: seoData.title?.length >= 30 && seoData.title?.length <= 60,
      },
      metaDescription: {
        exists: !!seoData.metaDescription,
        length: seoData.metaDescription?.length || 0,
        optimal:
          seoData.metaDescription?.length >= 120 &&
          seoData.metaDescription?.length <= 160,
      },
      headingStructure: {
        hasH1: seoData.headings.h1.length === 1,
        h1Count: seoData.headings.h1.length,
        hasH2: seoData.headings.h2.length > 0,
        h2Count: seoData.headings.h2.length,
      },
      images: {
        total: seoData.images.length,
        withAlt: seoData.images.filter((img) => img.hasAlt).length,
        withLazyLoading: seoData.images.filter((img) => img.hasLazyLoading)
          .length,
        altCoverage:
          seoData.images.length > 0
            ? (
                (seoData.images.filter((img) => img.hasAlt).length /
                  seoData.images.length) *
                100
              ).toFixed(1)
            : 100,
      },
      openGraph: {
        hasTitle: !!seoData.ogTitle,
        hasDescription: !!seoData.ogDescription,
        hasType: !!seoData.ogType,
        hasUrl: !!seoData.ogUrl,
      },
      accessibility: {
        hasMain: seoData.accessibility.mainLandmark,
        hasNav: seoData.accessibility.navLandmark,
        ariaLabels: seoData.accessibility.ariaLabels,
        score: 0,
      },
    };

    // 计算可访问性得分
    let accessibilityScore = 0;
    if (checks.accessibility.hasMain) accessibilityScore += 25;
    if (checks.accessibility.hasNav) accessibilityScore += 25;
    if (checks.accessibility.ariaLabels > 0) accessibilityScore += 25;
    if (checks.headingStructure.hasH1) accessibilityScore += 25;
    checks.accessibility.score = accessibilityScore;

    // 输出结果
    console.log('📋 SEO检查结果:\n');

    console.log('🏷️  页面标题:');
    console.log(
      `   ${checks.title.exists ? '✅' : '❌'} 标题存在: "${seoData.title}"`
    );
    console.log(
      `   ${checks.title.optimal ? '✅' : '⚠️'} 长度优化: ${checks.title.length}字符 (推荐30-60)`
    );

    console.log('\n📝 Meta描述:');
    console.log(`   ${checks.metaDescription.exists ? '✅' : '❌'} 描述存在`);
    console.log(
      `   ${checks.metaDescription.optimal ? '✅' : '⚠️'} 长度优化: ${checks.metaDescription.length}字符 (推荐120-160)`
    );

    console.log('\n📊 标题结构:');
    console.log(
      `   ${checks.headingStructure.hasH1 ? '✅' : '❌'} H1标题: ${checks.headingStructure.h1Count}个`
    );
    console.log(
      `   ${checks.headingStructure.hasH2 ? '✅' : '❌'} H2标题: ${checks.headingStructure.h2Count}个`
    );

    console.log('\n🖼️  图片优化:');
    console.log(`   📊 总图片数: ${checks.images.total}`);
    console.log(
      `   ${checks.images.altCoverage >= 90 ? '✅' : '⚠️'} Alt文本覆盖率: ${checks.images.altCoverage}%`
    );
    console.log(
      `   ${checks.images.withLazyLoading > 0 ? '✅' : '⚠️'} 懒加载图片: ${checks.images.withLazyLoading}个`
    );

    console.log('\n📱 Open Graph:');
    console.log(`   ${checks.openGraph.hasTitle ? '✅' : '❌'} OG标题`);
    console.log(`   ${checks.openGraph.hasDescription ? '✅' : '❌'} OG描述`);
    console.log(`   ${checks.openGraph.hasType ? '✅' : '❌'} OG类型`);
    console.log(`   ${checks.openGraph.hasUrl ? '✅' : '❌'} OG URL`);

    console.log('\n♿ 可访问性:');
    console.log(`   ${checks.accessibility.hasMain ? '✅' : '❌'} Main地标`);
    console.log(`   ${checks.accessibility.hasNav ? '✅' : '❌'} Nav地标`);
    console.log(
      `   ${checks.accessibility.ariaLabels > 0 ? '✅' : '⚠️'} ARIA标签: ${checks.accessibility.ariaLabels}个`
    );
    console.log(`   📊 可访问性得分: ${checks.accessibility.score}/100`);

    // 计算总体SEO得分
    let seoScore = 0;
    if (checks.title.exists && checks.title.optimal) seoScore += 20;
    if (checks.metaDescription.exists && checks.metaDescription.optimal)
      seoScore += 20;
    if (checks.headingStructure.hasH1 && checks.headingStructure.hasH2)
      seoScore += 20;
    if (checks.images.altCoverage >= 90) seoScore += 15;
    if (checks.openGraph.hasTitle && checks.openGraph.hasDescription)
      seoScore += 15;
    if (checks.accessibility.score >= 75) seoScore += 10;

    console.log(`\n🎯 SEO总体得分: ${seoScore}/100`);
    console.log(
      `   评级: ${seoScore >= 80 ? '🟢 优秀' : seoScore >= 60 ? '🟡 良好' : '🔴 需要改进'}`
    );

    return {
      seoData,
      checks,
      seoScore,
      passed: seoScore >= 70,
    };
  } catch (error) {
    console.error('❌ SEO测试失败:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  testSEOOptimization()
    .then((results) => {
      console.log(`\n🏁 SEO验证完成: ${results.passed ? '通过' : '需要优化'}`);
      process.exit(results.passed ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testSEOOptimization };
