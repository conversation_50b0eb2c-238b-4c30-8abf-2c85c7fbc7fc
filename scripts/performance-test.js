#!/usr/bin/env node

/**
 * 首页性能测试脚本
 * 验证页面加载速度和Core Web Vitals指标
 */

const puppeteer = require('puppeteer');

async function testPagePerformance() {
  console.log('🚀 启动首页性能测试...\n');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const page = await browser.newPage();

    // 设置视口大小
    await page.setViewport({ width: 1920, height: 1080 });

    // 开始性能监控
    const startTime = Date.now();

    // 导航到首页
    console.log('📄 加载首页...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000,
    });

    const loadTime = Date.now() - startTime;

    // 获取性能指标
    const metrics = await page.metrics();

    // 检查页面内容
    const heroTitle = await page.$eval('h1', (el) => el.textContent);
    const sectionsCount = await page.$$eval(
      '[class*="py-20"]',
      (els) => els.length
    );

    // 输出测试结果
    console.log('✅ 性能测试结果:');
    console.log(`   📊 页面加载时间: ${loadTime}ms`);
    console.log(
      `   🎯 LCP目标: < 2500ms (${loadTime < 2500 ? '✅ 通过' : '❌ 未通过'})`
    );
    console.log(
      `   🧠 JS堆大小: ${(metrics.JSHeapUsedSize / 1024 / 1024).toFixed(2)}MB`
    );
    console.log(`   📝 DOM节点数: ${metrics.Nodes}`);
    console.log(`   🎨 布局次数: ${metrics.LayoutCount}`);
    console.log(`   🖼️  重绘次数: ${metrics.RecalcStyleCount}`);

    console.log('\n📋 页面内容验证:');
    console.log(`   📰 Hero标题: "${heroTitle}"`);
    console.log(`   📦 组件区块数: ${sectionsCount}`);

    // 响应式测试
    console.log('\n📱 响应式测试:');

    // 移动端测试
    await page.setViewport({ width: 375, height: 667 });
    await page.reload({ waitUntil: 'networkidle0' });
    console.log('   ✅ 移动端 (375px) 加载正常');

    // 平板端测试
    await page.setViewport({ width: 768, height: 1024 });
    await page.reload({ waitUntil: 'networkidle0' });
    console.log('   ✅ 平板端 (768px) 加载正常');

    // 桌面端测试
    await page.setViewport({ width: 1920, height: 1080 });
    await page.reload({ waitUntil: 'networkidle0' });
    console.log('   ✅ 桌面端 (1920px) 加载正常');

    console.log('\n🎉 首页集成测试完成！');

    return {
      loadTime,
      metrics,
      heroTitle,
      sectionsCount,
      passed: loadTime < 2500,
    };
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  testPagePerformance()
    .then((results) => {
      console.log('\n📊 测试总结:');
      console.log(`   性能评分: ${results.passed ? '🟢 优秀' : '🟡 需要优化'}`);
      process.exit(results.passed ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testPagePerformance };
