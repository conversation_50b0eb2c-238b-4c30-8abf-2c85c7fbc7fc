#!/usr/bin/env node

/**
 * 响应式设计验证脚本
 * 测试不同设备尺寸下的布局表现
 */

const puppeteer = require('puppeteer');

const BREAKPOINTS = [
  { name: '手机端 (iPhone SE)', width: 375, height: 667 },
  { name: '手机端 (iPhone 12)', width: 390, height: 844 },
  { name: '平板端 (iPad)', width: 768, height: 1024 },
  { name: '平板端 (iPad Pro)', width: 1024, height: 1366 },
  { name: '桌面端 (笔记本)', width: 1366, height: 768 },
  { name: '桌面端 (台式机)', width: 1920, height: 1080 },
  { name: '大屏幕', width: 2560, height: 1440 },
];

async function testResponsiveDesign() {
  console.log('📱 启动响应式设计测试...\n');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const page = await browser.newPage();

    const results = [];

    for (const breakpoint of BREAKPOINTS) {
      console.log(
        `🔍 测试 ${breakpoint.name} (${breakpoint.width}x${breakpoint.height})`
      );

      // 设置视口
      await page.setViewport({
        width: breakpoint.width,
        height: breakpoint.height,
      });

      // 加载页面
      await page.goto('http://localhost:3000', {
        waitUntil: 'networkidle0',
        timeout: 15000,
      });

      // 检查布局元素
      const layoutChecks = await page.evaluate(() => {
        const checks = {};

        // 检查Hero区域
        const hero = document.querySelector('h1');
        if (hero) {
          const heroRect = hero.getBoundingClientRect();
          checks.heroVisible = heroRect.width > 0 && heroRect.height > 0;
          checks.heroFontSize = window.getComputedStyle(hero).fontSize;
        }

        // 检查导航栏
        const nav =
          document.querySelector('nav') || document.querySelector('header');
        if (nav) {
          const navRect = nav.getBoundingClientRect();
          checks.navVisible = navRect.width > 0 && navRect.height > 0;
        }

        // 检查Features网格
        const featuresGrid = document.querySelector('[class*="grid"]');
        if (featuresGrid) {
          const gridStyle = window.getComputedStyle(featuresGrid);
          checks.gridColumns = gridStyle.gridTemplateColumns;
        }

        // 检查按钮
        const buttons = document.querySelectorAll('button, [role="button"]');
        checks.buttonCount = buttons.length;
        checks.buttonsVisible = Array.from(buttons).every((btn) => {
          const rect = btn.getBoundingClientRect();
          return rect.width > 0 && rect.height > 0;
        });

        // 检查容器宽度
        const container = document.querySelector('[class*="container"]');
        if (container) {
          checks.containerWidth = container.getBoundingClientRect().width;
        }

        // 检查是否有水平滚动条
        checks.hasHorizontalScroll =
          document.body.scrollWidth > window.innerWidth;

        return checks;
      });

      // 截图保存（可选）
      // await page.screenshot({
      //   path: `screenshots/responsive-${breakpoint.width}x${breakpoint.height}.png`,
      //   fullPage: true
      // });

      results.push({
        breakpoint,
        layoutChecks,
        passed:
          !layoutChecks.hasHorizontalScroll &&
          layoutChecks.heroVisible &&
          layoutChecks.navVisible,
      });

      console.log(
        `   ${layoutChecks.hasHorizontalScroll ? '❌' : '✅'} 无水平滚动条`
      );
      console.log(`   ${layoutChecks.heroVisible ? '✅' : '❌'} Hero区域可见`);
      console.log(`   ${layoutChecks.navVisible ? '✅' : '❌'} 导航栏可见`);
      console.log(
        `   ${layoutChecks.buttonsVisible ? '✅' : '❌'} 按钮正常显示`
      );
      console.log(`   📏 容器宽度: ${layoutChecks.containerWidth}px`);
      console.log(`   🔤 Hero字体大小: ${layoutChecks.heroFontSize}`);
      console.log('');
    }

    // 输出总结
    const passedCount = results.filter((r) => r.passed).length;
    const totalCount = results.length;

    console.log('📊 响应式测试总结:');
    console.log(
      `   通过率: ${passedCount}/${totalCount} (${Math.round((passedCount / totalCount) * 100)}%)`
    );

    if (passedCount === totalCount) {
      console.log('   🎉 所有断点测试通过！');
    } else {
      console.log('   ⚠️  部分断点需要优化');
      results
        .filter((r) => !r.passed)
        .forEach((result) => {
          console.log(`   ❌ ${result.breakpoint.name}: 需要修复`);
        });
    }

    return {
      results,
      passedCount,
      totalCount,
      allPassed: passedCount === totalCount,
    };
  } catch (error) {
    console.error('❌ 响应式测试失败:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  testResponsiveDesign()
    .then((summary) => {
      console.log(
        `\n🏁 测试完成: ${summary.allPassed ? '全部通过' : '部分失败'}`
      );
      process.exit(summary.allPassed ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testResponsiveDesign };
