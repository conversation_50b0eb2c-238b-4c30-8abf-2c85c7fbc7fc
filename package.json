{"name": "flood-control-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "npx prettier --write .", "format:check": "npx prettier --check .", "prepare": "husky", "pre-commit": "lint-staged", "clean": "rm -rf .next .contentlayer dist build", "analyze": "ANALYZE=true next build"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "contentlayer": "0.3.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.14.0", "lucide-react": "^0.344.0", "next": "14.2.0", "next-contentlayer": "0.3.4", "next-intl": "3.8.0", "next-themes": "^0.4.6", "react": "18.2.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-wrap-balancer": "^1.1.1", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.14.1", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-next": "14.2.0", "eslint-import-resolver-typescript": "^4.3.5", "postcss": "^8.4.35", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "5.4.5"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@10.8.0", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md,mdx}": ["prettier --write"]}}