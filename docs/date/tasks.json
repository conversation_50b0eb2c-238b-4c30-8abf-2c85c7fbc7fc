{"tasks": [{"id": "79bb7ccf-30d8-4116-a589-716fa29b38d3", "name": "修复现有组件硬编码文本问题", "description": "检查并修复当前首页组件中存在的硬编码文本问题，确保所有显示文本都使用翻译函数，符合项目的国际化规范。", "notes": "重点检查 ProductPortfolio、ApplicationScenarios、CompanyStrength 组件中的硬编码问题，如 Badge 组件中的文本、状态指示器文本等。", "status": "pending", "dependencies": [], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "修复第50-53行的硬编码'即将推出'文本", "lineStart": 48, "lineEnd": 54}, {"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "修复第32行和第77行的硬编码文本", "lineStart": 30, "lineEnd": 80}, {"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "修复第21-28行Badge组件和第47行的硬编码文本", "lineStart": 20, "lineEnd": 50}, {"path": "messages/zh.json", "type": "TO_MODIFY", "description": "添加缺失的翻译键值", "lineStart": 1, "lineEnd": 644}, {"path": "messages/en.json", "type": "TO_MODIFY", "description": "同步添加英文翻译键值", "lineStart": 1, "lineEnd": 637}], "implementationGuide": "1. 检查 src/components/home/<USER>/components/home/<USER>/English strings)\\n  extract to translation keys\\n  replace with t('namespace.key')\\n  update both zh.json and en.json\\n```", "verificationCriteria": "1. 所有组件中无硬编码显示文本\\n2. 翻译文件中包含所有必要的键值\\n3. 中英文翻译文件保持同步\\n4. 页面在两种语言下正常显示\\n5. 通过 ESLint 检查无硬编码警告", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "eac3109f-dfbd-4110-9fa9-82fb07f6e96c", "name": "设计新的翻译文件结构", "description": "为重新设计的模块化防汛系统组件创建完整的翻译文件结构，包括行业洞察、产品重新定位、系统化展示等内容的中英文翻译。", "notes": "翻译结构需要支持参数化翻译，特别是数字和百分比数据。确保键名使用 camelCase 格式，符合项目规范。", "status": "pending", "dependencies": [{"taskId": "79bb7ccf-30d8-4116-a589-716fa29b38d3"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "messages/zh.json", "type": "TO_MODIFY", "description": "添加 modularSystemV2 完整翻译结构", "lineStart": 82, "lineEnd": 107}, {"path": "messages/en.json", "type": "TO_MODIFY", "description": "添加对应的英文翻译结构", "lineStart": 82, "lineEnd": 107}], "implementationGuide": "1. 在 messages/zh.json 中添加 web.home.modularSystemV2 命名空间\\n2. 设计三个主要部分的翻译结构：industryInsights、transition、systemDisplay\\n3. 包含行业趋势数据、产品协同信息、层级展示内容\\n4. 同步创建英文翻译版本\\n\\nPseudocode:\\n```\\nmodularSystemV2: {\\n  industryInsights: {\\n    title: string,\\n    subtitle: string,\\n    trends: Array<{title, keyMetric, description, impact}>\\n  },\\n  transition: {\\n    title: string,\\n    description: string,\\n    productReposition: Array<{name, newRole}>\\n  },\\n  systemDisplay: {\\n    title: string,\\n    layers: Array<{title, products, synergy, scenario, value, metrics}>\\n  }\\n}\\n```", "verificationCriteria": "1. 翻译文件结构完整，包含所有必要的键值\\n2. 中英文翻译内容准确对应\\n3. 支持参数化翻译的动态内容\\n4. 键名符合 camelCase 命名规范\\n5. JSON 格式正确，无语法错误", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "df84a912-c130-441f-966e-d8200c5a96bb", "name": "实现行业洞察区域组件", "description": "开发模块化防汛系统的第一部分：行业洞察区域，展示防汛行业的三个重要变化，包括数据可视化和趋势分析。", "notes": "使用现有的设计系统组件（Badge、Card等），保持与其他首页组件的视觉一致性。重点突出数据的权威性和专业性。", "status": "pending", "dependencies": [{"taskId": "eac3109f-dfbd-4110-9fa9-82fb07f6e96c"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "重写组件的第一部分：行业洞察区域", "lineStart": 16, "lineEnd": 60}, {"path": "src/components/ui/badge.tsx", "type": "REFERENCE", "description": "使用 Badge 组件显示趋势标签"}, {"path": "src/components/ui/balancer.tsx", "type": "REFERENCE", "description": "使用 Balancer 组件优化标题显示"}], "implementationGuide": "1. 创建行业洞察区域的 JSX 结构\\n2. 实现三个趋势卡片的布局和样式\\n3. 添加数据可视化元素（关键指标、图表）\\n4. 使用 Lucide 图标增强视觉效果\\n5. 实现响应式设计\\n\\nPseudocode:\\n```\\nfunction IndustryInsightsSection() {\\n  const trends = [\\n    {title, keyMetric, description, impact},\\n    // 三个趋势数据\\n  ];\\n  \\n  return (\\n    <section className='industry-insights'>\\n      <header>{title + subtitle}</header>\\n      <div className='trends-grid'>\\n        {trends.map(trend => (\\n          <TrendCard key={trend.title} data={trend} />\\n        ))}\\n      </div>\\n    </section>\\n  );\\n}\\n```", "verificationCriteria": "1. 行业洞察区域正确渲染三个趋势卡片\\n2. 数据可视化元素清晰易读\\n3. 响应式设计在移动端和桌面端都正常显示\\n4. 使用翻译函数，无硬编码文本\\n5. 视觉风格与其他首页组件保持一致", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "42471a85-fb8e-4d59-ac82-cd232ce11a38", "name": "实现解决方案过渡区域组件", "description": "开发模块化防汛系统的第二部分：解决方案过渡区域，展示从行业观察到产品组合策略的逻辑过渡，包括产品重新定位。", "notes": "这个区域是整个组件的关键过渡部分，需要清晰地表达从单品到系统的思维转变。使用视觉元素帮助用户理解逻辑关系。", "status": "pending", "dependencies": [{"taskId": "df84a912-c130-441f-966e-d8200c5a96bb"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "添加解决方案过渡区域的实现", "lineStart": 61, "lineEnd": 90}], "implementationGuide": "1. 创建过渡区域的洞察总结部分\\n2. 实现产品重新定位的可视化展示\\n3. 添加连接线或箭头表示转换关系\\n4. 使用渐变背景和装饰元素增强视觉效果\\n5. 确保逻辑流程清晰易懂\\n\\nPseudocode:\\n```\\nfunction TransitionSection() {\\n  const productReposition = [\\n    {name: '自吸水袋', newRole: '快速响应组件'},\\n    {name: 'ABS防洪板', newRole: '标准防护组件'},\\n    {name: '铝合金挡水板', newRole: '强化防护组件'}\\n  ];\\n  \\n  return (\\n    <section className='transition-section'>\\n      <InsightSummary />\\n      <ProductRepositioning products={productReposition} />\\n    </section>\\n  );\\n}\\n```", "verificationCriteria": "1. 过渡逻辑清晰，用户能理解从观察到方案的转换\\n2. 产品重新定位展示直观明了\\n3. 视觉设计与前后区域协调统一\\n4. 响应式布局在不同设备上正常显示\\n5. 所有文本内容使用翻译函数", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "6dfa97d7-9b10-49d4-a154-34e6d41beb61", "name": "实现系统化展示区域组件", "description": "开发模块化防汛系统的第三部分：系统化展示区域，展示三层防护体系，包括产品组合、协同效应、应用场景和价值优势。", "notes": "这是组件的核心展示区域，需要详细展现系统化方案的价值。使用丰富的视觉元素和数据支撑，增强说服力。", "status": "pending", "dependencies": [{"taskId": "42471a85-fb8e-4d59-ac82-cd232ce11a38"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "重写三层防护体系展示部分", "lineStart": 91, "lineEnd": 170}], "implementationGuide": "1. 设计三层防护体系的可视化布局\\n2. 为每层创建详细的信息卡片\\n3. 展示产品组合和协同效应\\n4. 添加成功指标和案例数据\\n5. 实现连接线表示层级关系\\n6. 添加悬停效果和交互动画\\n\\nPseudocode:\\n```\\nfunction SystemDisplaySection() {\\n  const layers = [\\n    {\\n      title: '第一层：外围快速封堵',\\n      products: [{name, role, feature}],\\n      synergy: '协同效应描述',\\n      scenario: '应用场景',\\n      value: '核心价值',\\n      metrics: {successRate: '90%', description: '部署时间缩短'}\\n    }\\n    // 三层数据\\n  ];\\n  \\n  return (\\n    <section className='system-display'>\\n      <SystemTitle />\\n      <LayersContainer>\\n        {layers.map((layer, index) => (\\n          <LayerCard key={index} layer={layer} index={index} />\\n        ))}\\n      </LayersContainer>\\n    </section>\\n  );\\n}\\n```", "verificationCriteria": "1. 三层防护体系展示完整清晰\\n2. 产品组合和协同效应信息准确\\n3. 成功指标和数据具有说服力\\n4. 交互效果流畅，用户体验良好\\n5. 整体设计与前面区域形成完整的信息流", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "92369602-09dd-4a8e-b406-68dd543b8f7b", "name": "添加底部行动召唤区域", "description": "为重新设计的模块化防汛系统组件添加底部的行动召唤区域，引导用户进行系统化方案咨询，与整体设计保持一致。", "notes": "CTA区域应该自然地承接前面的内容，强调系统化方案的价值，引导用户采取行动。按钮样式和交互应与其他首页组件保持一致。", "status": "pending", "dependencies": [{"taskId": "6dfa97d7-9b10-49d4-a154-34e6d41beb61"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "添加底部行动召唤区域", "lineStart": 171, "lineEnd": 174}, {"path": "src/components/home/<USER>", "type": "REFERENCE", "description": "参考现有的咨询组件设计模式"}, {"path": "src/components/ui/button.tsx", "type": "REFERENCE", "description": "使用标准的 Button 组件"}], "implementationGuide": "1. 设计行动召唤区域的布局和样式\\n2. 添加引导性文案和按钮\\n3. 与现有的 ProfessionalConsultation 组件保持一致\\n4. 实现渐变背景和视觉装饰\\n5. 确保响应式设计\\n\\nPseudocode:\\n```\\nfunction CTASection() {\\n  return (\\n    <section className='cta-section'>\\n      <div className='gradient-background'>\\n        <h4>{t('cta.title')}</h4>\\n        <p>{t('cta.description')}</p>\\n        <Button onClick={handleConsultation}>\\n          {t('cta.button')}\\n        </Button>\\n      </div>\\n    </section>\\n  );\\n}\\n```", "verificationCriteria": "1. CTA区域设计与整体组件风格一致\\n2. 引导文案清晰有效，突出系统化价值\\n3. 按钮交互正常，样式符合设计规范\\n4. 响应式布局在不同设备上正常显示\\n5. 与其他首页组件的CTA保持一致性", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}, {"id": "f3b95a61-ee33-4f4d-bf00-237df492fa2e", "name": "组件集成测试和优化", "description": "对重新设计的模块化防汛系统组件进行全面测试，包括功能测试、响应式测试、国际化测试和性能优化。", "notes": "重点测试组件的国际化功能，确保所有文本都正确翻译。同时验证组件与整个首页的协调性，确保用户体验的连贯性。", "status": "pending", "dependencies": [{"taskId": "92369602-09dd-4a8e-b406-68dd543b8f7b"}], "createdAt": "2025-05-28T01:22:32.903Z", "updatedAt": "2025-05-28T01:22:32.903Z", "relatedFiles": [{"path": "src/components/home/<USER>", "type": "TO_MODIFY", "description": "完整的重新设计组件", "lineStart": 1, "lineEnd": 174}, {"path": "src/app/[locale]/page.tsx", "type": "REFERENCE", "description": "验证组件在首页中的集成效果"}, {"path": "messages/zh.json", "type": "REFERENCE", "description": "验证中文翻译的完整性"}, {"path": "messages/en.json", "type": "REFERENCE", "description": "验证英文翻译的完整性"}], "implementationGuide": "1. 测试组件在不同设备和浏览器上的显示效果\\n2. 验证中英文切换功能正常\\n3. 检查所有交互元素的响应性\\n4. 优化组件性能和加载速度\\n5. 确保无障碍性要求\\n6. 验证与其他首页组件的集成效果\\n\\nPseudocode:\\n```\\nfunction testModularSystemComponent() {\\n  // 功能测试\\n  testTranslationSwitching();\\n  testResponsiveLayout();\\n  testInteractiveElements();\\n  \\n  // 性能测试\\n  measureLoadTime();\\n  checkMemoryUsage();\\n  \\n  // 集成测试\\n  testWithOtherComponents();\\n  validateOverallUserFlow();\\n}\\n```", "verificationCriteria": "1. 组件在 Chrome、Firefox、Safari 等主流浏览器正常显示\\n2. 移动端和桌面端响应式布局完美适配\\n3. 中英文切换功能正常，所有文本正确翻译\\n4. 组件加载性能良好，无明显延迟\\n5. 与其他首页组件集成无冲突，整体用户体验流畅\\n6. 通过无障碍性检查，支持屏幕阅读器", "analysisResult": "重新设计模块化防汛系统区块，采用行业趋势洞察式设计方案。通过三个部分（行业洞察区域30%、解决方案过渡区域20%、系统化展示区域50%）展现从单个产品到系统化组合的价值升级，解决现有组件的硬编码文本问题，确保完全符合项目的国际化规范和设计系统一致性。"}]}