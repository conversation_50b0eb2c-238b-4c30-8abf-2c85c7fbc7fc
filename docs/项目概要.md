# 图森堡防洪设备网站项目概要

## 1. 项目目标

图森堡防洪设备网站是一个专业的多语言产品展示平台，旨在向全球客户展示公司的防洪设备产品线和解决方案。

### 1.1 主要目标

- 构建专业、现代化的企业形象展示平台
- 详细展示防洪设备产品系列及其技术参数
- 提供多语言支持（中文/英文），满足国内外客户需求
- 通过专业内容建立行业权威形象，提升品牌认知度
- 收集潜在客户信息，形成高质量销售线索

### 1.2 目标受众

- **专业采购人员**：寻找防洪设备解决方案的政府部门、企业采购人员
- **工程承包商**：负责防洪工程实施的建筑和工程公司
- **应急管理部门**：需要应急防洪设备的政府和企业应急管理部门
- **国际客户**：寻找中国制造防洪设备的海外客户

### 1.3 预期成果

- 提升品牌在线可见度和专业形象
- 增加产品咨询和报价请求数量
- 建立完整的产品信息数据库，方便客户查询
- 通过多语言支持拓展国际市场
- 提供良好的用户体验，降低客户获取信息的门槛

## 2. 技术方案

### 2.1 技术栈概述

本项目采用现代化的前端技术栈，以确保网站的性能、可维护性和扩展性：

- **核心框架**：Next.js 14（App Router架构）
- **UI渲染**：React 18
- **类型系统**：TypeScript
- **样式解决方案**：Tailwind CSS
- **UI组件库**：Shadcn UI（基于Radix UI）
- **内容管理**：Contentlayer + MDX
- **国际化**：next-intl
- **表单处理**：React Hook Form + Zod
- **动画效果**：Framer Motion
- **分析工具**：Vercel Analytics
- **错误监控**：Sentry

### 2.2 多语言支持实现

项目通过以下方式实现多语言支持：

1. **路由结构**：使用`[locale]`动态路由参数（如`/zh/products`和`/en/products`）
2. **中间件处理**：通过Next.js中间件检测用户语言偏好并重定向
3. **翻译资源**：按功能模块组织的JSON翻译文件
4. **内容本地化**：使用Contentlayer管理多语言结构化内容
5. **组件国际化**：
   - 服务器组件使用`getTranslations`函数
   - 客户端组件使用`useTranslations`钩子

### 2.3 内容管理系统

项目使用Contentlayer管理结构化内容：

1. **内容模型**：为产品、案例、新闻等定义类型化内容模型
2. **MDX支持**：使用MDX格式编写富文本内容，支持React组件嵌入
3. **内容验证**：通过模式定义实现内容字段验证
4. **多语言内容**：通过locale和translationKey字段关联不同语言的内容
5. **计算字段**：自动生成URL、格式化日期等派生数据

### 2.4 响应式设计与性能优化

- **移动优先**：采用移动优先的响应式设计方法
- **图像优化**：使用Next.js图像组件自动优化图像加载和格式
- **代码分割**：利用Next.js的自动代码分割减少初始加载时间
- **服务器组件**：优先使用React服务器组件减少客户端JavaScript
- **静态生成**：尽可能使用静态生成提高页面加载速度
- **缓存策略**：实施有效的缓存策略减少重复请求

## 3. 项目结构

### 3.1 代码库文件夹结构

```
/
├── src/                      # 源代码目录
│   ├── app/                  # Next.js App Router目录
│   │   ├── [locale]/         # 多语言路由
│   │   │   ├── page.tsx      # 首页
│   │   │   ├── products/     # 产品页面
│   │   │   ├── about/        # 关于页面
│   │   │   └── ...
│   ├── components/           # React组件
│   │   ├── layout/           # 布局组件
│   │   ├── ui/               # UI基础组件
│   │   ├── products/         # 产品相关组件
│   │   └── mdx/              # MDX增强组件
│   ├── lib/                  # 工具函数和服务
│   ├── hooks/                # 自定义React钩子
│   ├── i18n/                 # 国际化配置和翻译
│   │   ├── locales/          # 翻译文件
│   │   │   ├── zh/           # 中文翻译
│   │   │   └── en/           # 英文翻译
│   └── styles/               # 全局样式
├── content/                  # 内容文件（MDX）
│   ├── products/             # 产品内容
│   ├── cases/                # 案例内容
│   ├── news/                 # 新闻内容
│   └── about/                # 关于内容
├── public/                   # 静态资源
│   ├── images/               # 图片资源
│   └── fonts/                # 字体资源
└── ...                       # 配置文件
```

### 3.2 组件分类

项目组件按功能分为以下几类：

1. **布局组件**：
   - Header、Footer、Container等页面布局组件
   - 负责整体页面结构和导航

2. **UI基础组件**：
   - Button、Card、Input等基础UI元素
   - 基于Shadcn UI，保持设计一致性

3. **功能组件**：
   - ProductCard、ProductGallery等特定功能组件
   - 实现特定业务逻辑和数据展示

4. **页面组件**：
   - 对应特定路由的页面级组件
   - 组合其他组件构建完整页面

### 3.3 内容组织

产品数据以MDX文件形式存储，结构如下：

```
content/
├── products/
│   ├── water-bags/           # 自吸水袋系列
│   │   ├── fb-100.mdx        # FB-100产品
│   │   └── ...
│   ├── abs-barriers/         # ABS防洪板系列
│   └── ...
├── cases/                    # 案例研究
├── news/                     # 新闻文章
└── about/                    # 关于页面内容
```

每个MDX文件包含前置元数据（frontmatter）和正文内容，通过Contentlayer处理成结构化数据供组件使用。

## 4. 总结

图森堡防洪设备网站项目是一个使用现代前端技术栈构建的多语言产品展示平台。项目采用Next.js 14、Contentlayer、Tailwind CSS和Shadcn UI组件，通过[locale]动态路由实现多语言支持，使用Contentlayer管理结构化内容。

项目组件按功能分为布局组件、UI基础组件、功能组件和页面组件，产品数据以MDX文件形式存储并通过专门的组件展示。目前已完成基础架构搭建、多语言支持框架实现和产品展示系统基础实现，下一步将重点开发信息中心、联系表单及产品筛选功能。

通过这个项目，图森堡防洪设备将建立专业的在线形象，向全球客户展示其产品和解决方案，提升品牌认知度并获取高质量销售线索。