
# 防汛设备企业网站项目任务规划

## Plan

### 项目概述

#### 全局项目愿景
本项目采用分阶段开发策略：**第一阶段完成next-forge的1:1完全复刻**，创建高质量的现代化Web应用基础模板；**第二阶段基于复刻模板进行防汛设备企业网站的定制开发**。

**第一阶段目标（当前阶段）- Next-Forge 1:1完全复刻**：
- **界面完全复刻**：1:1复制next-forge的所有页面布局、组件设计、视觉效果
- **交互体验复刻**：完全复制用户交互流程、动画效果、响应式行为
- **设计系统复刻**：完整复制颜色系统、字体层次、间距规范、组件变体
- **代码直接复用**：采用直接复制代码的高效方式，确保功能和样式的完全一致
- **技术栈替换**：使用Contentlayer+next-intl替代next-forge的CMS和国际化方案
- **模板化输出**：创建可复用的高质量基础模板，适用于后续项目开发

**第二阶段目标（后续规划）- 防汛设备企业定制**：
- **专业产品展示**：防汛设备产品线完整展示、技术参数详细说明、应用场景介绍
- **行业权威性建立**：技术文档、行业资讯、专业知识分享，建立行业领导地位
- **客户服务体系**：产品手册、安装指南、维护文档、常见问题解答
- **业务拓展支持**：多语言支持、地区适配、询盘系统、客户案例展示

**技术架构愿景**：
- **现代化基础**：基于next-forge复刻的现代化设计系统和组件库
- **性能优化**：静态生成、CDN分发、图片优化，确保快速加载
- **SEO友好**：结构化数据、元标签优化、搜索引擎可见性
- **内容管理**：基于文件的内容管理，支持版本控制和协作编辑
- **响应式设计**：移动优先，适配各种设备和屏幕尺寸
- **可维护性**：清晰的代码结构、组件化开发、文档完善

#### 当前阶段（Next-Forge 1:1复刻）
采用Next.js 14.2.0 + React 18.2.0 + TypeScript核心技术栈，使用Contentlayer+next-intl进行内容管理和国际化，UI系统基于Tailwind CSS + Shadcn UI。当前阶段目标是**完全复刻next-forge的界面、交互和设计系统**，创建高质量的现代化Web应用基础模板，为后续的防汛设备企业网站定制开发奠定基础。

### 技术栈策略
- **核心框架**：Next.js 14.2.0、React 18.2.0、TypeScript 5.4.0（版本锁定）
- **内容管理**：Contentlayer + MDX（替代next-forge的@repo/cms）
- **国际化**：next-intl 3.8.0（替代next-forge的@repo/internationalization）
- **UI系统**：Tailwind CSS + Shadcn UI（与next-forge保持一致）
- **架构选择**：标准Next.js单体应用（避免过度工程化的monorepo）
- **next-forge策略**：1:1完全复刻，采用直接复制代码的高效方式

### Next-Forge 1:1复刻阶段任务列表

> **注意**：以下任务列表专门针对next-forge的1:1完全复刻，旨在创建高质量的现代化Web应用基础模板。防汛设备企业网站的定制开发将在复刻完成后的第二阶段进行。

#### 1. 根目录文件清理和整理
- **描述**：清理根目录中的临时文件、示例文件和冗余配置，为项目初始化做准备
- **关键点**：移除临时文件、清理示例适配组件、重命名next-forge为参考资料、保留重要配置文件

#### 2. 项目架构重构为标准Next.js单体应用
- **描述**：将当前的turbo monorepo架构重构为标准的Next.js单体应用架构
- **关键点**：创建新的package.json配置、移除turbo相关配置、确保版本锁定符合PRD要求
- **依赖**：根目录文件清理和整理

#### 3. MVP核心依赖安装和基础配置
- **描述**：安装MVP阶段的核心依赖包，配置基础的开发环境
- **关键点**：安装框架、UI组件、国际化、内容管理等必需组件，确保项目可以正常启动
- **依赖**：项目架构重构为标准Next.js单体应用

#### 4. 标准Next.js项目目录结构建立
- **描述**：根据PRD要求和Next.js最佳实践，建立标准的项目目录结构
- **关键点**：创建App Router的多语言路由结构，组织组件、工具函数、样式等目录
- **依赖**：MVP核心依赖安装和基础配置

#### 5. Shadcn UI组件系统初始化
- **描述**：初始化Shadcn UI组件系统，安装和配置基础UI组件
- **关键点**：建立组件库的基础架构，为后续的UI开发和next-forge设计借鉴做准备
- **依赖**：标准Next.js项目目录结构建立

#### 6. 国际化系统配置（next-intl）
- **描述**：配置next-intl国际化系统，支持中英文双语
- **关键点**：建立翻译文件结构，配置路由和中间件，确保多语言功能正常工作
- **依赖**：Shadcn UI组件系统初始化

#### 7. Contentlayer内容管理系统配置
- **描述**：配置Contentlayer内容管理系统，支持MDX内容
- **关键点**：建立内容模型定义，配置多语言内容结构，为产品、案例、新闻等内容类型做准备
- **依赖**：国际化系统配置（next-intl）

#### 8. Next-Forge页面和组件1:1完全复刻
- **描述**：1:1完全复刻next-forge的所有页面和组件，包括首页、定价页、文档页、布局组件等
- **关键点**：直接复制代码，完全复刻界面设计、交互效果、响应式行为，确保视觉和功能的完全一致
- **复刻策略**：直接复制组件代码→适配技术栈差异→集成Contentlayer+next-intl→验证功能完整性
- **依赖**：Contentlayer内容管理系统配置

#### 9. 复刻模板优化和文档完善
- **描述**：优化复刻模板的代码质量，完善使用文档，为后续项目开发准备基础模板
- **关键点**：代码重构优化、性能调优、创建使用指南、建立定制开发规范
- **依赖**：Next-Forge页面和组件1:1完全复刻

### 关键决策记录

#### Next-Forge复刻阶段决策
1. **复刻策略**：采用1:1完全复刻而非"学习借鉴"，确保界面和功能的完全一致性
2. **代码复用方式**：直接复制代码的高效方式，而非重新开发，提高开发效率和质量保证
3. **技术栈替换**：保持现有Contentlayer+next-intl技术栈，替代next-forge的CMS和国际化方案
4. **架构选择**：继续使用标准Next.js单体应用，避免monorepo的复杂性
5. **版本锁定**：核心框架版本严格按照PRD要求，确保稳定性

#### 分阶段开发决策
1. **第一阶段**：专注于next-forge的完全复刻，创建高质量基础模板
2. **第二阶段**：基于复刻模板进行防汛设备企业网站的定制开发
3. **模板化输出**：将复刻结果作为可复用模板，适用于其他项目开发
4. **质量优先**：确保复刻的完整性和准确性，为后续定制开发奠定坚实基础

#### 长期架构决策
1. **模块化设计**：组件和功能模块化，便于维护和功能扩展
2. **内容优先**：建立灵活的内容管理架构，支持产品、案例、文档等多种内容类型
3. **性能导向**：优先考虑加载速度和用户体验，适合企业网站的访问模式
4. **SEO友好**：从架构层面确保搜索引擎优化，提升网站可见性
5. **国际化基础**：建立完整的多语言架构，支持业务国际化需求

### 风险控制

- **参考资料保护**：保留reference-next-forge作为完整的复刻参考
- **技术栈兼容性**：确保Contentlayer+next-intl与复刻组件的完美集成
- **渐进式复刻**：按组件模块逐步复刻，每个阶段都有明确的验收标准
- **质量保证**：建立复刻质量检查机制，确保视觉和功能的完全一致性
- **回滚机制**：确保每个复刻步骤都可以安全回退
- **模板保存**：及时保存复刻成果，创建可复用的基础模板

## Task - Next-Forge复刻阶段任务执行记录

> **范围说明**：本部分记录next-forge 1:1完全复刻阶段的任务执行情况。复刻完成后，将启动第二阶段的防汛设备企业网站定制开发任务。

### ✅ 已完成任务

#### 任务1：根目录文件清理和整理
- **任务ID**：`d3a3e7c9-143c-47cf-993e-de3caf3abf7f`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：95/100）

**任务目标**：
清理根目录中的临时文件、示例文件和冗余配置，为项目初始化做准备。移除不必要的文件避免安装配置时的混乱，同时保留有价值的配置文件和参考资料。

**主要成果**：
1. **临时文件清理**：成功移除6个临时文件
   - component-migration-guide.md
   - component-optimization-example.tsx
   - design-analysis-plan.md
   - replication-comparison.tsx
   - seo-optimization-example.ts
   - optimized-package.json

2. **示例代码清理**：删除了适配组件示例
   - src/components/adapted/ 目录及其内容
   - src/lib/adapters/ 目录及其内容
   - src/components/ui/button-enhanced.tsx

3. **架构清理**：移除monorepo相关配置
   - packages/ 目录
   - pnpm-workspace.yaml
   - turbo.json
   - web/ 目录

4. **参考资料保留**：将next-forge重命名为reference-next-forge

5. **重要文件保护**：确保关键配置文件完整保留
   - next.config.js（包含Contentlayer和国际化配置）
   - tailwind.config.js（包含完整的设计系统配置）
   - docs/ 目录完整保留

**遇到的挑战及解决方案**：
- **目录删除权限问题**：使用shell命令配合remove-files工具解决
- **monorepo残留识别**：系统性检查并清理所有相关配置文件
- **重要文件保护**：明确识别需要保留的文件，清理后进行验证

**验证结果**：
- ✅ 根目录中不存在临时文件和示例文件
- ✅ next-forge目录已重命名为reference-next-forge
- ✅ 重要配置文件（next.config.js、tailwind.config.js）保持不变
- ✅ docs目录完整保留
- ✅ 根目录结构清晰，为后续安装做好准备

**最终状态**：
根目录现在只包含必要文件和目录：docs/、src/、reference-next-forge/、next.config.js、tailwind.config.js、package.json。项目已为架构重构和依赖安装做好准备。

---

#### 任务2：项目架构重构为标准Next.js单体应用
- **任务ID**：`a7fbb175-970d-4a7c-8b5d-2570e7a56593`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：95/100）

**任务目标**：
将turbo monorepo架构重构为标准的Next.js单体应用架构，符合MVP策略和企业网站需求。

**主要成果**：
1. **monorepo残留清理**：成功移除packages和web目录
2. **package.json重构**：创建标准Next.js应用配置，包含正确的核心框架版本
3. **版本锁定确认**：Next.js 14.2.0、React 18.2.0、TypeScript 5.4.5
4. **依赖配置**：包含MVP阶段所需的所有核心依赖

**遇到的挑战及解决方案**：
- **文件操作权限问题**：使用shell命令配合文件工具解决
- **package.json重建**：通过完全重新创建确保配置正确性
- **版本兼容性**：严格按照PRD要求锁定核心框架版本

#### 任务3：MVP核心依赖安装和基础配置
- **任务ID**：`9ec3351b-615a-4d96-be51-7d85faa0097b`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：92/100）

**任务目标**：
安装MVP阶段的核心依赖包，配置基础的开发环境，确保项目可以正常启动和开发。

**主要成果**：
1. **依赖安装完成**：所有核心依赖已正确安装，包括框架、UI组件、国际化、内容管理等
2. **配置文件创建**：tsconfig.json、.eslintrc.json、.prettierrc、postcss.config.js、contentlayer.config.js
3. **基础应用结构**：创建App Router结构、layout和page组件、全局CSS配置
4. **功能验证**：项目可以正常构建和启动开发服务器

**遇到的挑战及解决方案**：
- **TypeScript版本问题**：查询可用版本，更新为TypeScript 5.4.5
- **Tailwind CSS插件缺失**：安装缺失的tailwindcss-animate依赖
- **Contentlayer配置缺失**：创建配置文件和示例内容
- **TypeScript配置优化**：排除reference-next-forge目录避免类型错误

#### 任务4：标准Next.js项目目录结构建立
- **任务ID**：`959a1c9d-ad48-4b8e-96cf-09a3f39a8955`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：94/100）

**任务目标**：
根据PRD要求和Next.js最佳实践，建立标准的项目目录结构，创建App Router的多语言路由结构。

**主要成果**：
1. **App Router多语言结构**：创建`src/app/[locale]`路由结构，支持动态locale参数
2. **组件目录架构**：layout、ui、shared组件分类，建立清晰的组件层次
3. **工具和类型系统**：lib工具函数、constants常量、types类型定义、hooks自定义钩子
4. **内容和资源管理**：content内容目录、public静态资源目录结构

**遇到的挑战及解决方案**：
- **路由结构重构**：重新组织app目录，使用[locale]动态路由参数
- **依赖管理**：安装@radix-ui/react-slot等缺失依赖
- **Next.js缓存问题**：清理.next目录并重新构建
- **ESLint配置冲突**：简化ESLint配置，移除未安装的TypeScript规则

#### 任务5：Shadcn UI组件系统初始化
- **任务ID**：`22ed110d-aa8b-4dfb-81ba-5692ce4351e9`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：96/100）

**任务目标**：
初始化Shadcn UI组件系统，安装和配置基础UI组件，建立组件库的基础架构。

**主要成果**：
1. **Shadcn UI初始化**：完成shadcn/ui配置，生成components.json配置文件
2. **核心组件安装**：Button、Card、Input、Label、Select、Textarea、Badge等基础组件
3. **高级组件集成**：DropdownMenu、NavigationMenu、Sheet、Dialog、Alert等交互组件
4. **组件系统架构**：Header组件升级、ContactForm表单组件、组件展示页面

**遇到的挑战及解决方案**：
- **包名更新问题**：使用正确的`npx shadcn@latest init`命令
- **组件覆盖确认**：选择覆盖现有组件以使用官方标准
- **工具函数丢失**：手动恢复项目特定的工具函数
- **响应式导航实现**：使用NavigationMenu和Sheet实现完美的移动端适配

#### 任务6：国际化系统配置（next-intl）
- **任务ID**：`b8a0fa85-71d1-46fa-a062-d7a1f7a0e89f`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：92/100）

**任务目标**：
配置next-intl国际化系统，支持中英文双语，建立翻译文件结构，配置路由和中间件。

**主要成果**：
1. **next-intl基础配置**：完成i18n.ts配置文件，支持中英文语言切换
2. **翻译文件结构**：建立messages/zh.json和messages/en.json翻译文件
3. **中间件配置**：配置middleware.ts处理语言路由和重定向
4. **组件国际化**：更新layout.tsx和Header组件支持多语言

**遇到的挑战及解决方案**：
- **路由配置复杂性**：通过中间件正确处理语言前缀路由
- **翻译文件组织**：建立清晰的翻译键值结构
- **组件集成**：确保所有组件正确使用翻译函数

#### 任务7：Contentlayer内容管理系统配置
- **任务ID**：`35447a49-e758-4be5-a73c-0d842ba5d7be`
- **完成时间**：2024年12月
- **执行状态**：✅ 已完成（评分：96/100）

**任务目标**：
配置Contentlayer内容管理系统，支持MDX内容，建立内容模型定义，配置多语言内容结构，为产品、案例、新闻等内容类型做准备。

**主要成果**：
1. **完整的内容模型定义**：成功定义了Product（产品）、Case（案例）、News（新闻）、Page（页面）四种内容类型
2. **MDX处理配置**：集成了rehype-slug、rehype-autolink-headings、remark-gfm、rehype-pretty-code等插件
3. **多语言内容结构**：建立了完整的中英文双语内容目录结构，支持locale-based的URL生成
4. **类型安全系统**：创建了完整的TypeScript类型定义和工具函数，确保编译时类型检查
5. **示例内容创建**：为每种内容类型创建了符合业务需求的示例内容文件
6. **页面功能验证**：成功创建了产品列表页、产品详情页、新闻列表页，并在首页集成了内容展示

**遇到的挑战及解决方案**：
- **TypeScript类型导入问题**：更新tsconfig.json包含路径，修改导入语句使用正确路径
- **类型安全问题**：明确指定类型注解，使用类型断言确保类型安全
- **内容字段验证**：清理内容文件，确保所有字段都符合模型定义
- **MDX插件配置**：系统性安装所需插件，配置rehype和remark插件链

**验证结果**：
- ✅ Contentlayer配置正确，成功生成8个文档
- ✅ 内容模型定义完整，支持防汛设备相关字段
- ✅ 多语言内容结构清晰，URL生成正确
- ✅ 示例内容可以正常生成和访问
- ✅ TypeScript类型定义正确，提供类型安全
- ✅ 页面功能正常，构建成功无错误

**最终状态**：
Contentlayer内容管理系统已完全配置完成，支持产品、案例、新闻、页面四种内容类型的管理，具备完整的多语言支持和类型安全保障，为后续的基础页面和组件开发提供了强大的内容管理基础。

---

### ⏳ 待执行任务

#### 下一个任务：Next-Forge页面和组件1:1完全复刻
- **任务ID**：`8a1608ef-f8d9-4907-abd1-0e2c43295b27`
- **状态**：进行中（设计系统统一已完成）
- **依赖**：Contentlayer内容管理系统配置（已完成）
- **预计工作量**：3-4天
- **关键风险**：代码适配复杂性、技术栈集成、响应式设计一致性
- **复刻策略**：直接复制代码→适配技术栈差异→集成Contentlayer+next-intl→验证功能完整性

#### 后续任务队列
1. 复刻模板优化和文档完善

### 📊 Next-Forge复刻阶段进度

- **总任务数**：9个
- **已完成**：7个（77.8%）
- **进行中**：1个（11.1%）
- **待执行**：1个（11.1%）

**关键路径**：前七个基础任务已完成，项目具备了标准Next.js单体应用架构、完整的开发环境配置、清晰的目录结构、现代化的UI组件系统、国际化支持和内容管理系统。当前正在进行第八阶段的next-forge页面和组件1:1完全复刻工作，设计系统统一已完成。

**下一阶段预览**：复刻阶段完成后，将启动**第二阶段：防汛设备企业网站定制开发**，基于复刻模板进行行业特定的定制开发。