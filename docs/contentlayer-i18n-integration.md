# Contentlayer与next-intl集成方案

## 概述

本文档描述了项目中Contentlayer内容管理系统与next-intl国际化系统的集成方案，明确了两个系统的职责分工，并解决了硬编码翻译文本的问题。

## 架构设计

### 职责分工

```
┌─────────────────┬──────────────────────────────────────┐
│   Contentlayer  │ 管理内容数据的多语言版本                │
│                 │ - 产品描述、案例详情、新闻内容          │
│                 │ - 通过文件路径区分语言                  │
│                 │ - 生成带语言前缀的URL                   │
├─────────────────┼──────────────────────────────────────┤
│    next-intl    │ 管理所有界面文本的翻译                  │
│                 │ - 页面标题、按钮、标签                  │
│                 │ - 表单字段、错误信息                    │
│                 │ - 导航菜单、面包屑                      │
└─────────────────┴──────────────────────────────────────┘
```

### 内容数据管理（Contentlayer）

**文件结构**：
```
content/
├── products/
│   ├── zh/flood-barrier-system.md    # 中文产品内容
│   └── en/flood-barrier-system.md    # 英文产品内容
├── cases/
│   ├── zh/city-center-protection.md  # 中文案例内容
│   └── en/city-center-protection.md  # 英文案例内容
├── news/
│   ├── zh/flood-season-preparation.md
│   └── en/flood-season-preparation.md
└── pages/
    ├── zh/about.md
    └── en/about.md
```

**URL生成策略**：
- 产品：`/{locale}/products/{slug}`
- 案例：`/{locale}/cases/{slug}`
- 新闻：`/{locale}/news/{slug}`
- 页面：`/{locale}/{slug}`

**关键配置**：
```javascript
// contentlayer.config.js
computedFields: {
  url: {
    type: 'string',
    resolve: (product) => {
      const pathParts = product._raw.flattenedPath.split('/');
      const locale = pathParts[1]; // products/zh/product-name -> zh
      const slug = pathParts[2]; // products/zh/product-name -> product-name
      return `/${locale}/products/${slug}`;
    },
  },
  locale: {
    type: 'string',
    resolve: (product) => {
      const pathParts = product._raw.flattenedPath.split('/');
      return pathParts[1]; // 从文件路径自动提取语言
    },
  }
}
```

### 界面文本管理（next-intl）

**翻译文件结构**：
```
messages/
├── zh.json    # 中文界面翻译
└── en.json    # 英文界面翻译
```

**翻译文件组织**：
```json
{
  "pages": {
    "products": {
      "title": "产品中心",
      "subtitle": "专业的防汛设备解决方案",
      "category": "分类",
      "publishDate": "发布日期",
      "readingTime": "阅读时间",
      "featured": "特色",
      "noProducts": "暂无产品信息"
    },
    "cases": {
      "title": "案例展示",
      "location": "项目地点",
      "client": "客户"
    },
    "news": {
      "title": "新闻资讯",
      "author": "作者",
      "category": "分类"
    }
  },
  "breadcrumb": {
    "home": "首页",
    "products": "产品",
    "cases": "案例",
    "news": "新闻"
  },
  "content": {
    "featuredProducts": "特色产品",
    "latestNews": "最新资讯"
  }
}
```

## 实施改进

### 1. 扩展翻译文件

**问题**：页面中存在硬编码的翻译文本
```typescript
// 改进前 - 硬编码
{params.locale === 'zh' ? '产品中心' : 'Products'}
```

**解决方案**：使用next-intl翻译系统
```typescript
// 改进后 - 使用翻译
const t = useTranslations('pages.products');
{t('title')}
```

### 2. 统一翻译管理

**改进的页面组件**：
```typescript
import { useTranslations } from 'next-intl';

export default function ProductsPage({ params }: ProductsPageProps) {
  const t = useTranslations('pages.products');
  const products = getAllProducts(params.locale);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-4">
        {t('title')}  // 使用翻译而不是硬编码
      </h1>
      <p className="text-lg text-muted-foreground">
        {t('subtitle')}
      </p>
      {/* ... */}
    </div>
  );
}
```

### 3. 创建可复用组件

**面包屑导航组件**：
```typescript
// src/components/shared/breadcrumb.tsx
export function Breadcrumb({ locale, type, slug, title }: BreadcrumbProps) {
  const t = useTranslations('breadcrumb');
  
  // 自动生成面包屑路径
  const breadcrumbs = generateBreadcrumbs(type, locale, slug, title);
  
  return (
    <nav className="text-sm">
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((item, index) => (
          // 渲染面包屑项目
        ))}
      </ol>
    </nav>
  );
}
```

## 使用示例

### 产品页面
```typescript
// 获取内容数据（Contentlayer）
const products = getAllProducts(params.locale);

// 获取界面翻译（next-intl）
const t = useTranslations('pages.products');

// 渲染
<h1>{t('title')}</h1>
{products.map(product => (
  <div key={product._id}>
    <h2>{product.title}</h2>  {/* 来自Contentlayer */}
    <span>{t('category')}: {product.category}</span>  {/* 混合使用 */}
  </div>
))}
```

### 面包屑导航
```typescript
<Breadcrumb 
  locale={params.locale}
  type={ContentType.PRODUCT}
  slug={params.slug}
  title={product.title}
/>
```

## 优势

### 1. 清晰的关注点分离
- **Contentlayer**：专注于内容数据管理
- **next-intl**：专注于界面文本翻译

### 2. 维护性提升
- 所有界面翻译集中管理
- 避免硬编码翻译文本
- 统一的翻译键值结构

### 3. 开发体验改善
- TypeScript类型安全
- IDE智能提示
- 组件复用性

### 4. 性能优化
- 构建时内容处理
- 静态生成支持
- 最小化运行时开销

## 最佳实践

### 1. 翻译键值命名
- 使用层级结构：`pages.products.title`
- 保持一致的命名规范
- 避免过深的嵌套

### 2. 内容文件组织
- 按类型和语言分目录
- 保持文件名一致性
- 使用有意义的slug

### 3. 组件设计
- 创建可复用的翻译组件
- 避免在组件中硬编码翻译
- 使用TypeScript确保类型安全

### 4. 性能考虑
- 合理使用静态生成
- 避免不必要的客户端渲染
- 优化翻译文件大小

## 总结

通过明确Contentlayer和next-intl的职责分工，我们实现了：

1. **内容数据**由Contentlayer管理，支持多语言文件结构
2. **界面翻译**由next-intl统一管理，避免硬编码
3. **组件复用**通过创建翻译组件提高开发效率
4. **类型安全**通过TypeScript确保代码质量

这种架构设计既保持了系统的灵活性，又提供了良好的开发体验和维护性。
