## 项目产品需求文档 (PRD) - v2.2

**版本历史:**
*   v1.0: 初始草稿
*   v1.1: 技术选型确定 (Next.js App Router, Tailwind, Decap CMS -> MDX), NFRs 细化
*   v2.0: 确认最终技术栈，明确业务信息 (产品系列)，确认开发流程 (结构与 UI 同步)
*   v2.1: 基于内容结构方案和设计方案进行全面优化，确保文档一致性
*   **v2.2: 补充关键绩效指标、用户角色与故事、项目范围界定和非功能性需求详情**

### 1. 项目概述

#### 1.1 企业背景
  Tucsenberg成立于2010年，是一家专注于防洪设备研发、设计与制造的企业。我们致力于为全球用户提供质量过硬的多种防汛产品系列，并基于这些产品打造适用于各种特殊防汛场景的综合防洪解决方案，帮助终端用户有效应对各类洪水威胁。
  企业优势：深耕行业上下游产业链路，基于中国国内强大的供应链提供高性价比、质量过硬的一系列防洪产品。
#### 1.2 核心目标
打造一个面向防洪设备、器材生产企业的专业企业官网，核心目的是通过专业的产品展示与企业理念传达（定制化防洪安全方案、模块化防洪系统），吸引潜在客户（尤其是经销商和采购单位）主动填写沟通表单，形成高质量销售线索，最大程度提升主动询盘数量。

#### 1.3 目标用户
*   **经销商（主要，80%）**：位于洪水灾害频发地区，具备渠道资源，关注产品性价比、系列组合、供货能力及贴牌可能性。
*   **采购单位/个人（次要，20%）**：如企事业单位、物业公司、个人（住宅/仓库）等，具有明确的防洪需求，关注产品性能、实际案例、定制能力及售后服务。
 **地理覆盖或期待地区（按照优先排序）**：美洲、东南亚、日本、欧洲地区。


#### 1.4 解决问题
*   提升企业在目标市场的品牌曝光度与专业形象。
*   提供结构化、可视化、易于理解的产品信息（含技术参数、应用场景），突出“定制化”与“模块化”理念。
*   构建高效、安全的线索收集机制（联系表单），促进销售转化。
*   建立支持未来多语言扩展的基础架构。

#### 1.5 关键绩效指标 (KPI)
项目成功将通过以下量化指标进行衡量，每个指标都明确了责任归属和数据来源：

| 指标类别 | 指标名称 | 目标值 | 责任归属 | 数据来源 | 测量频率 |
|---------|---------|-------|---------|---------|---------|
| **流量目标** | 网站月均访问量 | ≥ 5,000 次 | 市场部 | GA4 + Vercel Analytics | 每周 |
| | 平均会话时长 | ≥ 2分30秒 | 市场部 | GA4 | 每周 |
| | 跳出率 | ≤ 40% | 市场部 | GA4 | 每周 |
| | 移动端访问占比 | ≥ 60% | 市场部 | GA4 | 每周 |
| **转化目标** | 表单提交量 | ≥ 100条/30天 | 产品部 | Vercel KV + CRM | 每日 |
| | 产品页面到表单提交的转化率 | ≥ 3% | 产品部 | GA4 + Vercel KV | 每周 |
| | 首页到产品页面的点击率 | ≥ 25% | 产品部 | GA4 | 每周 |
| | 回访用户比例 | ≥ 15% | 市场部 | GA4 | 每周 |
| **业务目标** | 表单提交转化为实际商机比例 | ≥ 20% | 销售部 | CRM | 每周 |
| | 新增经销商数量 | ≥ 5家/季度 | 销售部 | CRM | 每月 |
| | 网站引导的直接销售额增长 | ≥ 15%/季度 | 销售部 | CRM | 每月 |
| | 品牌搜索量增长 | ≥ 30%/半年 | 市场部 | Google Search Console | 每月 |

**KPI数据收集与报告流程**：

1. **流量数据**：
   - 通过Google Analytics 4和Vercel Analytics自动收集
   - 市场部每周生成流量报告，分析趋势和异常
   - 使用自定义事件跟踪关键用户行为（如产品查看、表单点击）

2. **转化数据**：
   - 表单提交数据存储在Vercel KV，同步至CRM系统
   - 产品部负责监控转化漏斗，识别优化机会
   - 设置自动告警，当转化率低于目标值的80%时通知相关团队

3. **业务数据**：
   - 销售部在CRM中标记网站来源的线索和商机
   - 每月进行销售归因分析，评估网站对销售的贡献
   - 季度回顾会议评估KPI达成情况，调整策略

### 1.6 用户角色与故事
以下是主要用户角色的用户故事，每个故事都包含验收标准，可用于端到端测试的对齐和验证：

**经销商角色**
1. **用户故事**: 作为一名防洪设备经销商，我想了解产品的完整规格和价格区间，以便评估是否符合我的销售渠道需求。
   * **验收标准**:
     ```
     Given 我是一名经销商
     When 我访问产品中心页面
     Then 我应该能看到所有产品系列的详细规格表
     And 我应该能通过表单询问价格区间和批发政策
     ```

2. **用户故事**: 作为一名经销商，我想了解产品的定制和贴牌可能性，以便为我的客户提供更有针对性的解决方案。
   * **验收标准**:
     ```
     Given 我是一名经销商
     When 我浏览铝合金定制挡水板产品页面
     Then 我应该能看到定制选项和流程说明
     And 我应该能找到贴牌合作的相关信息
     ```

3. **用户故事**: 作为一名经销商，我想查看成功案例，以便向我的客户展示产品的实际应用效果。
   * **验收标准**:
     ```
     Given 我是一名经销商
     When 我访问案例分享页面
     Then 我应该能看到按应用场景分类的案例
     And 每个案例应包含问题描述、解决方案和效果展示
     ```

**采购人员角色**
1. **用户故事**: 作为一名政府防汛部门采购人员，我想了解产品的技术参数和认证情况，以便确保符合政府采购标准。
   * **验收标准**:
     ```
     Given 我是一名政府采购人员
     When 我查看产品详情页
     Then 我应该能找到完整的技术参数表
     And 我应该能看到相关认证和测试报告信息
     ```

2. **用户故事**: 作为一名企业安全负责人，我想了解防洪产品的安装和使用方法，以便评估实施难度。
   * **验收标准**:
     ```
     Given 我是一名企业安全负责人
     When 我浏览产品系列页面
     Then 我应该能找到安装指南和使用说明
     And 这些指南应包含图文步骤和视频演示
     ```

3. **用户故事**: 作为一名物业公司管理者，我想了解不同场景下的防洪解决方案组合，以便为不同类型的建筑选择合适的产品。
   * **验收标准**:
     ```
     Given 我是一名物业公司管理者
     When 我访问产品中心页的"模块化组合应用场景"部分
     Then 我应该能看到针对不同建筑类型的推荐组合
     And 每个组合应说明适用条件和预期效果
     ```

**个人用户角色**
1. **用户故事**: 作为一名住宅业主，我想快速了解适合家庭使用的防洪产品，以便在汛期保护我的财产安全。
   * **验收标准**:
     ```
     Given 我是一名住宅业主
     When 我从首页导航到产品页面
     Then 我应该能轻松找到标记为"适合家庭使用"的产品
     And 这些产品应有明确的价格范围和购买方式
     ```

2. **用户故事**: 作为一名仓库经营者，我想了解防洪产品的耐用性和存储要求，以便做出长期投资决策。
   * **验收标准**:
     ```
     Given 我是一名仓库经营者
     When 我查看产品详情
     Then 我应该能找到产品寿命和耐用性数据
     And 我应该能看到产品的存储条件和维护要求
     ```

### 1.7 项目范围界定
为防止开发过程中的范围蔓延(scope creep)，明确划分必须实现功能和可选功能：

**MVP必须实现功能**
| 功能 | 优先级 | 交付时间 | 描述 |
|------|--------|----------|------|
| 产品中心页 | P0 | 第1阶段 | 包含所有产品系列的详细信息，支持无刷新切换系列 |
| 产品详情页 | P0 | 第1阶段 | 单个产品的详细展示，包含技术参数和应用场景 |
| 统一咨询表单 | P0 | 第1阶段 | 产品页面底部的联系表单，支持reCAPTCHA验证 |
| 首页 | P1 | 第1阶段 | 企业形象展示与核心产品概览 |
| 企业简介页 | P1 | 第2阶段 | 公司介绍、历史、团队等信息 |
| 联系我们页 | P1 | 第2阶段 | 联系方式、地址、地图和联系表单 |
| 基础SEO优化 | P1 | 第1-2阶段 | 元标签、语义化HTML、sitemap.xml、robots.txt |
| 移动端响应式 | P1 | 全阶段 | 确保在所有设备上的良好体验 |

**Nice-to-Have可选功能**
| 功能 | 优先级 | 潜在交付时间 | 描述 |
|------|--------|--------------|------|
| 多语言支持 | P2 | 第4阶段 | 支持英文版本，包括内容翻译和语言切换器 |
| 案例分享页 | P2 | 第3阶段 | 成功案例展示，按应用场景分类 |
| 行业动态页 | P3 | 第3阶段 | 行业新闻、趋势和分析 |
| 企业动态页 | P3 | 第3阶段 | 公司新闻、活动和公告 |
| 灾害详情页 | P3 | 第3阶段 | 洪水灾害信息和防范知识 |
| 高级搜索功能 | P4 | 第4阶段 | 站内搜索，支持产品和内容搜索 |
| 产品筛选功能 | P4 | 第4阶段 | 按规格、应用场景等筛选产品 |
| 视频集成 | P4 | 第4阶段 | 产品演示和安装指南视频 |

#企业历程
我们的故事始于对洪水挑战的深刻洞察，以及为社会提供更可靠、更智能防洪解决方案的承诺。

创始初心：面对全球洪水灾害频发，我们怀揣"以科技守护安全"的初心成立，专注于防洪技术研究与核心材料突破。
核心产品线：逐步推出市场领先的防洪产品系列，包括轻便易储存的"自吸水袋系列"（替代20kg重传统沙袋，真空压缩包装便于运输）、快速布防可重复使用的"ABS组合式防洪板系列"及高强度的"铝合金定制挡水板"（为灾害敏感地带提供最强防护屏障）。
市场成就：从区域防洪设备供应商发展为行业领先企业，产品已应用于全国多个重点防洪工程，并逐步拓展国际市场，为全球客户提供可靠的防洪保障。
企业愿景
成为全球领先的智慧防洪解决方案提供者，用科技创新守护每一个生命与家园的安全。

我们致力于：

技术引领：持续投入研发，推出更智能、高效、环保的防洪产品与系统，引领行业技术发展方向。
安全守护：为全球面临洪水威胁的社区、企业和个人提供可靠防护，提升社会防洪韧性。
全球布局：建立全球化的产品供应和服务网络，针对不同地区的防洪需求提供本地化解决方案，成为国际防洪设备市场的重要参与者。
企业价值观
我们的企业文化和决策基于以下核心价值观：

客户为本
深入理解客户需求，提供卓越产品和专业解决方案，积极倾听反馈持续改进。
专业卓越
以严谨科学态度追求专业精进，打造高素质团队，确保产品可靠性和方案有效性。
责任担当
将保障生命财产安全作为根本使命，以诚信透明态度对待合作伙伴，积极履行企业社会责任

### 2. 核心功能 (MVP 范围)

#### 2.1 网站结构
网站将分为以下四个主要板块：

1. **首页** - 企业形象展示与核心产品概览
   * 路径: `/[locale]/`
   * 包含Hero区域、产品系列概览、核心优势、案例展示、行动召唤等板块

2. **产品** - 详细的产品系列与单品展示
   * 产品中心页: `/[locale]/products`（包含所有产品系列，使用URL参数切换系列），并展示基于这些产品打造适用于各种特殊防汛场景的综合防洪解决方案。
   * 产品详情页: `/[locale]/products/[slug]`
   * 四大产品系列:
     - 自吸水袋系列
     - ABS组合式防洪板系列
     - 铝合金定制挡水板
     - 排水泵系列（即将推出）

3. **信息** - 案例分享、行业动态、企业动态和灾害详情
   * 案例分享: `/[locale]/information/cases`
   * 行业动态: `/[locale]/information/industry-news`
   * 企业动态: `/[locale]/information/company-news`
   * 灾害详情: `/[locale]/information/disasters`

4. **关于** - 企业简介、企业愿景、企业价值观、业务合作FAQ和联系我们
   * 企业简介: `/[locale]/about/company`
   * 企业愿景: `/[locale]/about/vision`
   * 企业价值观: `/[locale]/about/values`
   * 业务合作FAQ: `/[locale]/about/faq`
   * 联系我们: `/[locale]/about/contact`

#### 2.2 产品展示内容
产品展示将采用二级结构：产品中心页和产品详情页。这种简化的结构旨在优化用户体验，减少页面跳转，使用户能够更快速地找到所需产品信息。

**产品中心页**将整合"模块化组合式防汛系统"的整体解决方案理念和各产品系列的详细信息，包含：

* **整体解决方案概述**
  - 企业防汛系统理念说明
  - 模块化组合的核心价值
  - 系统化防洪的优势

* **产品系列选择器**
  - 大型选项卡或图片选择器，用于切换不同产品系列
  - 四大产品系列展示
  - 选择后，页面平滑滚动到相应产品系列的详细内容

* **当前选中产品系列详细内容**
  - 产品系列介绍
  - 产品共有特点
  - 产品规格对比
  - 产品变体展示
  - 适用场景展示（可折叠面板）
  - 使用/安装指南（可折叠面板）
  - 产品FAQ（可折叠面板）

* **模块化组合应用场景**
  - 各类应用场景展示
  - 推荐产品组合

* **解决方案案例**
  - 精选成功案例展示

* **防汛知识科普**
  - 防汛小知识和指南

* **统一咨询表单**
  - 根据当前选中产品系列自动调整表单标题和预选项

**产品详情页**将展示单个产品的详细信息：
* 产品名称与型号
* 所属系列（带有返回产品中心页对应系列的链接）
* 状态（可用、敬请期待）
* 高清产品图（多角度，支持轮播/画廊）
* 详细描述
* 主要特点列表
* 技术参数表
* 应用场景图文描述
* 相关下载链接（如有）
* 产品咨询表单

**交互设计要点**：
* 使用客户端路由和状态管理，在不刷新页面的情况下切换产品系列内容
* 使用URL参数记录当前选择的产品系列，支持分享和书签
* 使用可折叠面板减少初始内容高度，提升用户体验
* 在移动设备上优化选择器和内容展示

#### 2.3 核心功能点
*   **多语言支持 (i18n):**
    *   **库:** `next-intl` (`3.8.0`)
    *   **策略:** 子路径 URL (`/en/`, `/zh/` 等，优先实现英文，中文预留)
    *   **内容存储:** 结构化内容使用 MDX，UI 文本使用语言文件
    *   **实现:** 遵循 next-intl 的 App Router 配置文档
*   **统一咨询表单:**
    *   **收集字段:**
        * 姓名（必填）
        * 公司/组织（选填）
        * 电话号码（必填）
        * 电子邮箱（必填）
        * 产品兴趣选择（多选，根据当前产品系列预选）
        * 需求描述/留言内容（文本区域）
        * 预期采购时间（下拉选择）
    *   **UI:** 使用 Shadcn `Form` 组件构建，提供客户端验证
    *   **安全防护:**
        *   Google reCAPTCHA v3 (前后端验证)
        *   服务端严格验证 (使用 Zod `^3.22.4`)
    *   **后端处理:** Next.js API Route (`/api/contact/route.ts`)
    *   **数据存储:** 提交的数据存储至 Vercel KV (`~1.2.0`)
    *   **邮件通知:** 使用 Resend (`^3.2.0`):
        *   向管理员邮箱发送新线索通知
        *   向提交者邮箱发送自动确认邮件
    *   **用户反馈:** 使用 Shadcn `Toast` 或 `Alert` 显示提交状态
*   **内容管理 (CMS):**
    *   **系统:** MDX (使用 `@next/mdx` 和 `contentlayer`)
    *   **工作流:** Git-based。产品内容 (MDX + Front Matter) 存储在 Git 仓库
    *   **管理内容:** MVP 阶段主要管理“产品”数据。
    *   **编辑方式:** 通过代码编辑器直接编辑MDX文件，支持版本控制。

### 3. 设计与用户体验 (UX)

#### 3.1 设计系统
项目将遵循"设计方案.md"中定义的统一设计标准，确保视觉一致性、品牌识别度和用户体验：

* **色彩系统:**
  * 主色: `#006D8B` - 品牌标识、主要按钮、重点强调
  * 强调色: `#00B4C8` - 次要按钮、链接、交互元素
  * 灰度色: `#0E1E2F` - 文本、边框、分隔线
  * 完整色阶定义 - 为每种基础色定义了完整的色阶，支持亮色和暗色主题

* **排版系统:**
  * 主要字体: Inter
  * 标题系统: 四级标题层级，从h1到h4，具有明确的字号、字重、行高和字母间距
  * 正文系统: 大号正文、标准正文、小号正文和说明文本，确保在各种设备上的最佳可读性
  * 响应式设计: 字体大小会根据屏幕尺寸自动调整

* **组件形态:**
  * 按钮: 主按钮、次按钮，具有统一的圆角、内边距和交互效果
  * 卡片: 标准卡片、强调型卡片，具有一致的圆角、阴影和悬停效果
  * 表单输入: 统一的输入框样式，聚焦状态和验证反馈
  * 内容区块间距标准化: 确保各主要内容区块之间的间距保持一致（24-32px）

* **交互体验:**
  * 交互元素微动效: 所有可交互元素添加hover和active状态的微动效
  * 按钮点击效果: 轻微的缩放效果和阴影变化
  * 页面切换和模态框过渡动画: 简洁的过渡动画，增强现代感
  * 主题切换实现: 支持亮色和暗色主题的无缝切换，防止主题切换闪烁

* **图标系统:**
  * 统一采用Lucide图标库，确保线条粗细一致
  * 图标颜色与文本保持一致
  * 响应式图标尺寸，确保在不同尺寸下保持清晰可辨识

#### 3.2 用户体验原则
* **一致性:** 整个应用程序使用统一的设计系统，确保品牌形象一致
* **可读性:** 字号、行高和字间距经过精心调整，确保在各种设备上的最佳可读性
* **层次结构:** 清晰的视觉层次帮助用户理解内容的重要性和关系
* **响应式:** 所有页面必须在常见设备尺寸（桌面、平板、手机）上具有良好的可用性和视觉效果
* **交互反馈:** 提供清晰的加载状态、表单验证反馈、提交成功/失败提示
* **可访问性:** 遵循WCAG 2.1 AA标准，确保所有用户都能良好地使用产品

### 4. 技术架构

#### 4.1 核心技术栈
*   **Node.js:** 20 LTS (`^20.9.0`)
*   **Next.js:** `~14.2.0` (App Router)
*   **React:** `^18.2.0`
*   **TypeScript:** `~5.4.0`
*   **CSS:** Tailwind CSS (`~3.4.1`)
*   **UI 组件库:** Shadcn/ui (Radix `^1.0.x` based)
*   **图标:** Lucide React (`~0.360.0`)
*   **国际化:** `next-intl` (`3.8.0`)
*   **内容管理:** MDX (使用 `@next/mdx` `^14.0.0` 和 `contentlayer` `^0.3.0`)
*   **表单验证:** Zod (`^3.22.4`)
*   **邮件发送:** Resend (`^3.2.0`)
*   **数据存储 (表单):** Vercel KV (`~1.2.0`)
*   **代码质量:** ESLint (`^8.55.0` or default), Prettier (`~3.2.0`)

#### 4.2 部署与托管
*   **平台:** Vercel。
*   **流程:** 连接到 Git 仓库，实现 CI/CD。`main` 分支自动部署生产，其他分支提供预览部署。

### 5. 非功能性需求 (NFRs) 与可观测性

#### 5.1 服务水平协议 (SLA)
* **系统可用性**: ≥ 99.9% (每月允许的最大停机时间约为43分钟)
* **页面加载时间**: 首次内容绘制 (FCP) ≤ 1.8秒 (对95%的用户请求)
* **API响应时间**: 表单提交处理 ≤ 2秒 (对99%的请求)
* **恢复时间目标 (RTO)**: ≤ 4小时 (从重大故障到恢复服务的最长时间)
* **恢复点目标 (RPO)**: ≤ 24小时 (可能丢失的最大数据时间范围)

#### 5.2 服务水平指标 (SLI)
**性能指标**
* **Core Web Vitals**:
  * 最大内容绘制 (LCP) < 2.5秒
  * 交互到下一次绘制 (INP) < 200毫秒
  * 累积布局偏移 (CLS) < 0.1
* **首次字节时间 (TTFB)**: < 800毫秒
* **首次内容绘制 (FCP)**: < 1.8秒
* **首次输入延迟 (FID)**: < 100毫秒

**可靠性指标**
* **成功率**: 表单提交成功率 > 99.5%
* **错误率**: 客户端JavaScript错误 < 0.1%/会话
* **CDN缓存命中率**: > 90%

**安全性指标**
* **漏洞修复时间**: 严重漏洞 < 24小时，高危漏洞 < 7天
* **依赖项扫描**: 每周自动执行，零高危漏洞
* **HTTPS评级**: SSL Labs A+评级

#### 5.3 实现策略

**性能优化**
* 利用 Next.js SSG/ISR 生成静态页面
* 使用 `next/image` 进行图像优化
* 通过 Vercel CDN 分发静态资源
* 实施代码分割和懒加载
* 监控工具: Lighthouse, Vercel Speed Insights, Web Vitals API

**安全措施**
* **表单安全**:
  * 服务端验证 (Zod `^3.22.4`)
  * Google reCAPTCHA v3 前后端验证
  * 速率限制 (考虑添加 `@upstash/ratelimit`)
  * Honeypot 字段防止自动提交
* **基础设施安全**:
  * Vercel 自动 HTTPS
  * 基础 DDoS 防护
  * 定期安全审计
* **应用层安全**:
  * 依赖项漏洞扫描 (`npm audit`)
  * 防止 XSS (安全的 Markdown 渲染)
  * 敏感信息 (API Keys) 存储在 Vercel 环境变量
* **内容安全**:
  * MDX 内容的安全渲染
  * 内容访问控制
  * 内容安全策略 (CSP)

**可访问性 (a11y)**
* 遵循 WCAG 2.1 AA 标准
* 实施语义化 HTML
* 确保所有图像有 alt 文本
* 支持键盘导航
* 确保颜色对比度符合标准
* 提供表单标签和错误提示
* 测试工具: Lighthouse, Axe DevTools

**SEO优化**
* **技术SEO**:
  * 利用 Next.js SSR/SSG
  * 移动优先响应式设计
  * 实现 `robots.txt` 和 `sitemap.xml` (使用 `next-sitemap`)
  * 使用 Next.js Metadata API 管理元标签
  * 实现 Open Graph 和 Twitter 卡片
* **On-Page SEO**:
  * 关键词研究与自然融入
  * 合理的标题层级结构
  * 高质量、原创内容
  * 内部链接策略
  * 图片优化 (alt 文本、压缩)
  * 结构化数据 (Schema.org)

**浏览器兼容性**
* 支持 Chrome, Firefox, Safari, Edge 最新两个稳定版本
* 不支持 IE11
* 使用 Browserslist 配置管理目标浏览器

**成本与资源管理**
* 监控 Resend, Vercel KV, reCAPTCHA 等服务的免费额度使用情况
* 设置使用量警报
* 制定资源扩展计划

### 6. 开发路线图

#### 6.1 MVP (最小可行产品)
1.  **P0-P2: 技术基础框架搭建 (已完成)**
2.  **P3: 产品内容管理与核心展示 (UI 同步)**
    *   MDX 产品内容结构定义 (使用 Contentlayer Schema)
    *   产品中心页 - 整合"模块化组合式防汛系统"的整体解决方案理念和各产品系列详细内容
    *   产品系列选择器 - 实现无刷新切换不同产品系列内容的交互功能
    *   产品详情页 - 单个产品的全面展示
    *   统一咨询表单组件 - 产品中心页和产品详情页底部都包含统一的咨询表单组件
    *   MDX 初步内容创建
3.  **P4: 核心交互 (联系表单) 与网站结构 (UI 同步)**
    *   联系表单后端 API
    *   联系表单前端页面 (含最终 UI)
    *   最终样式的 Header 和 Footer
    *   导航结构实现 - 主导航和页脚导航
4.  **P5: 首页构建与 MVP 收尾**
    *   首页开发 (含最终 UI)
        * Hero区域 - 展示企业核心价值主张
        * 产品系列概览 - 展示四大产品系列
        * 核心优势 - 突出企业的核心竞争力
        * 案例展示 - 精选成功案例
        * 行动召唤 - 引导用户联系或了解更多
    *   NFR 基础检查与初步优化 (Lighthouse, Axe)
    *   MVP 内容填充 (少量代表性产品)
    *   端到端测试 (用户流程, 跨浏览器/设备)
    *   生产环境部署与冒烟测试

#### 6.2 未来功能扩展 (按实施路线图)
1.  **阶段一 (已完成)**: 首页、产品中心页面

2.  **阶段二**: 关于板块的五个子页面
    * 企业简介
    * 企业愿景
    * 企业价值观
    * 业务合作FAQ
    * 联系我们

3.  **阶段三**: 信息板块的四个子版块
    * 案例分享
    * 行业动态
    * 企业动态
    * 灾害详情

4.  **阶段四**: 优化与完善
    * 多语言内容填充与切换器
    * SEO 深度优化 (Schema.org 结构化数据)
    * 高级功能 (产品筛选/搜索, 视频集成)
    * 性能和可访问性审计与优化

### 7. 项目计划与里程碑

#### 7.1 逻辑依赖链 (MVP 开发顺序)

1.  技术基础框架 (P0-P2)
2.  产品 Schema 定义 (P3.1)
3.  产品中心页（含产品系列选择器）& 详情页 (含 UI) (P3.2, P3.3)
4.  联系表单后端 API (P4.1)
5.  联系表单前端页面 (含 UI) (P4.2)
6.  Header & Footer (含 UI) (P4.3)
7.  首页构建 (P5)
8.  收尾、测试、部署 (P5)

#### 7.2 里程碑甘特图

以下甘特图展示了项目的关键里程碑和时间线，包括各团队成员的任务分配：

```
项目周期: 2023年Q4 - 2024年Q2
                  2023年Q4                |               2024年Q1               |               2024年Q2
任务/团队成员     10月    11月    12月    |    1月     2月     3月     |    4月     5月     6月
--------------------------------------------------------------------------------
【阶段一】MVP开发
技术基础框架      ████████ [FE1,FE2]
产品Schema定义           ████ [FE1,BE1]
产品中心页                    ████████ [FE1,UI1]
产品详情页                          ████████ [FE2,UI1]
联系表单后端                              ████ [BE1]
联系表单前端                                  ████ [FE2,UI1]
Header & Footer                                 ████ [FE1,UI1]
首页构建                                            ████████ [FE1,FE2,UI1]
测试与部署                                                ████ [QA1,DevOps]
MVP上线                                                    ◆ [ALL]

【阶段二】关于板块
企业简介页                                                     ████ [FE1,Content]
企业愿景页                                                         ████ [FE2,Content]
企业价值观页                                                           ████ [FE1,Content]
业务合作FAQ                                                               ████ [FE2,Content]
联系我们页                                                                   ████ [FE1,UI1]

【阶段三】信息板块
案例分享页                                                                       ████████ [FE2,Content]
行业动态页                                                                             ████ [FE1,Content]
企业动态页                                                                                 ████ [FE2,Content]
灾害详情页                                                                                     ████ [FE1,Content]

【阶段四】优化与完善
多语言支持                                                                                         ████████ [FE1,FE2]
SEO深度优化                                                                                               ████ [FE1,SEO]
高级功能                                                                                                     ████ [FE2,BE1]
性能优化                                                                                                         ████ [FE1,DevOps]
```

**团队成员角色**:
- FE1, FE2: 前端开发工程师
- BE1: 后端开发工程师
- UI1: UI设计师
- QA1: 测试工程师
- DevOps: 运维工程师
- Content: 内容编辑
- SEO: SEO专家
- ALL: 全体团队成员

#### 7.3 GTM协同说明

为确保网站与营销活动的协同，我们制定了以下Go-To-Market协作计划：

1. **落地页模板**：
   - 为营销团队提供3种标准化落地页模板，支持活动、产品推广和季节性促销
   - 模板位置：`/[locale]/campaigns/[campaign-id]`
   - 模板包含：自定义Hero区域、产品展示、表单收集、倒计时组件
   - 营销团队可通过CMS自行更新内容，无需开发介入

2. **活动追踪**：
   - 实现UTM参数跟踪系统，记录用户来源和活动归属
   - 在Vercel KV中存储UTM数据，与表单提交关联
   - 提供活动效果实时仪表板，展示转化率和ROI

3. **协作流程**：
   - 营销活动计划提前2周提交，技术团队评估可行性
   - 每周一进行营销-技术协调会议，同步进度和解决问题
   - 活动上线前进行负载测试，确保系统稳定性
   - 活动结束后3天内提供完整数据报告

4. **资源分配**：
   - 每月预留10个工程师工时用于支持营销活动
   - 为大型活动（如行业展会）预留额外资源
   - 建立营销技术支持快速响应机制，SLA为4小时内响应

### 8. 风险矩阵与缓解措施

以下风险矩阵详细列出了项目可能面临的风险，包括风险级别、检测指标、触发阈值和责任人：

| 风险类别 | 风险描述 | 影响程度<br>(1-5) | 发生概率<br>(1-5) | 风险级别<br>(影响×概率) | 检测指标 | 触发阈值 | 责任人 | 缓解措施 |
|---------|---------|:---------------:|:---------------:|:--------------------:|---------|---------|--------|---------|
| **技术风险** | MDX与Contentlayer集成复杂 | 3 | 4 | 12<br>**中** | 开发延期天数 | >3天 | 前端开发负责人 | 参考官方文档和示例，从简单内容模型开始，逐步完善 |
| | `next-intl` App Router配置困难 | 3 | 3 | 9<br>**中** | 国际化功能Bug数 | >5个 | 前端开发负责人 | 严格遵循最新官方文档，参考社区示例 |
| | 免费套餐限制 | 4 | 2 | 8<br>**中** | 服务使用量 | >80%免费额度 | 技术运营负责人 | 上线初期监控，预估增长，准备升级或替代方案 |
| **安全风险** | 表单垃圾邮件 | 3 | 4 | 12<br>**中** | 垃圾提交率 | >10% | 安全负责人 | 结合后端速率限制，添加`@upstash/ratelimit`，实现Honeypot字段 |
| | 数据泄露 | 5 | 1 | 5<br>**低** | 安全审计警告 | 任何高危警告 | 安全负责人 | 敏感信息加密存储，定期安全审计 |
| **设计风险** | UI风格统一性难以保持 | 3 | 3 | 9<br>**中** | 设计一致性审查不通过项 | >3项 | UI设计负责人 | 依赖Shadcn/ui主题化，共享通用组件，定期Code Review |
| | 内容结构一致性难以保持 | 3 | 3 | 9<br>**中** | 内容结构审查不通过项 | >3项 | 内容负责人 | 创建页面模板，定期与内容结构方案对照检查 |
| **业务风险** | 表单转化率低于预期 | 4 | 3 | 12<br>**中** | 表单转化率 | <2% | 产品负责人 | A/B测试不同表单设计，优化用户体验 |
| | 内容更新频率不足 | 3 | 4 | 12<br>**中** | 内容更新间隔 | >30天 | 内容负责人 | 建立内容日历，设置定期更新提醒 |
| **项目风险** | 开发进度延迟 | 4 | 3 | 12<br>**中** | 里程碑延期 | >5个工作日 | 项目经理 | 采用敏捷开发，设置缓冲期，关键路径监控 |
| | 需求变更频繁 | 4 | 3 | 12<br>**中** | 需求变更数 | >3项/周 | 产品负责人 | 明确变更流程，评估影响，调整优先级 |

**风险监控与响应流程**：

1. **定期风险评估**：
   - 每周项目例会中回顾风险矩阵
   - 每两周更新风险状态和缓解措施进展
   - 每月进行全面风险重评估

2. **风险触发响应**：
   - 当检测指标达到触发阈值时，责任人必须在24小时内制定应对计划
   - 高风险项目（风险级别≥15）需要每日跟进，直至风险降级
   - 中风险项目（风险级别9-14）需要每周跟进
   - 低风险项目（风险级别≤8）需要每两周跟进

3. **风险升级机制**：
   - 当风险无法在责任人层面解决时，升级至项目指导委员会
   - 对业务有重大影响的风险（影响程度≥4）必须通知高管团队

