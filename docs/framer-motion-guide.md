# Framer Motion 使用指南

## 概述

本项目已成功集成 Framer Motion 动画库，提供了一套完整的动画组件和配置，确保在整个应用中保持一致的动画体验。

## 安装的组件

### 1. 动画配置 (`src/lib/animations.ts`)

包含预定义的动画变体和过渡效果：

- `transitions` - 各种过渡配置（default, quick, smooth, bouncy, linear）
- `fadeVariants` - 淡入淡出动画
- `slideVariants` - 滑动动画（从四个方向）
- `scaleVariants` - 缩放动画
- `hoverVariants` - 悬停效果
- `staggerContainer` & `staggerItem` - 交错动画
- `pageVariants` - 页面过渡动画
- `cardVariants` - 卡片动画
- `heroVariants` - Hero 区域专用动画

### 2. 可复用动画组件 (`src/components/ui/motion.tsx`)

#### 基础动画组件

```tsx
import { FadeIn, SlideIn, ScaleIn } from '@/components/ui/motion';

// 淡入动画
<FadeIn delay={0.2} duration={0.5}>
  <div>内容</div>
</FadeIn>

// 滑动动画
<SlideIn direction="up" delay={0.1} distance={50}>
  <div>内容</div>
</SlideIn>

// 缩放动画
<ScaleIn delay={0.3} scale={0.8}>
  <div>内容</div>
</ScaleIn>
```

#### 交互动画组件

```tsx
import { HoverScale, AnimatedCard } from '@/components/ui/motion';

// 悬停缩放效果
<HoverScale scale={1.05}>
  <button>按钮</button>
</HoverScale>

// 动画卡片
<AnimatedCard hoverEffect={true} delay={0.2}>
  <div>卡片内容</div>
</AnimatedCard>
```

#### 列表动画组件

```tsx
import { StaggerContainer, StaggerItem } from '@/components/ui/motion';

// 交错动画容器
<StaggerContainer staggerDelay={0.1} childrenDelay={0.2}>
  <StaggerItem>
    <div>项目 1</div>
  </StaggerItem>
  <StaggerItem>
    <div>项目 2</div>
  </StaggerItem>
  <StaggerItem>
    <div>项目 3</div>
  </StaggerItem>
</StaggerContainer>
```

#### 滚动触发动画

```tsx
import { RevealOnScroll } from '@/components/ui/motion';

// 滚动时显示
<RevealOnScroll 
  direction="up" 
  threshold={0.1} 
  triggerOnce={true}
  delay={0.2}
>
  <div>滚动时显示的内容</div>
</RevealOnScroll>
```

#### 特殊效果

```tsx
import { Floating } from '@/components/ui/motion';

// 浮动动画
<Floating duration={3} yOffset={10}>
  <div>浮动元素</div>
</Floating>
```

### 3. 页面过渡组件 (`src/components/ui/page-transition.tsx`)

```tsx
import { PageTransition, SectionTransition } from '@/components/ui/page-transition';

// 页面过渡
<PageTransition key={pathname}>
  <div>页面内容</div>
</PageTransition>

// 区域过渡
<SectionTransition direction="up" delay={0.2}>
  <section>区域内容</section>
</SectionTransition>
```

## 使用示例

### Hero 区域动画（已实现）

Hero 组件已经集成了完整的动画效果：

- 使用 `StaggerContainer` 创建整体的交错动画
- 公告按钮使用 `FadeIn` 淡入效果
- 标题和描述使用 `SlideIn` 从下方滑入
- CTA 按钮也使用 `SlideIn` 延迟显示

### 为其他组件添加动画

#### 1. 产品卡片动画

```tsx
import { AnimatedCard, RevealOnScroll } from '@/components/ui/motion';

export const ProductCard = ({ product }) => (
  <RevealOnScroll direction="up" delay={0.1}>
    <AnimatedCard hoverEffect={true}>
      <div className="p-6">
        <h3>{product.title}</h3>
        <p>{product.description}</p>
      </div>
    </AnimatedCard>
  </RevealOnScroll>
);
```

#### 2. 功能列表动画

```tsx
import { StaggerContainer, StaggerItem, SlideIn } from '@/components/ui/motion';

export const FeatureList = ({ features }) => (
  <StaggerContainer staggerDelay={0.15}>
    {features.map((feature, index) => (
      <StaggerItem key={index}>
        <SlideIn direction="left" delay={index * 0.1}>
          <div className="feature-item">
            <h4>{feature.title}</h4>
            <p>{feature.description}</p>
          </div>
        </SlideIn>
      </StaggerItem>
    ))}
  </StaggerContainer>
);
```

## 最佳实践

### 1. 性能优化

- 使用 `triggerOnce={true}` 避免重复触发滚动动画
- 合理设置 `threshold` 值控制触发时机
- 避免在大量元素上同时使用复杂动画

### 2. 用户体验

- 保持动画时长适中（0.3-0.6秒）
- 使用适当的延迟创建层次感
- 确保动画符合用户预期的方向和行为

### 3. 响应式设计

- 在移动设备上可以减少动画效果
- 使用 CSS 媒体查询控制动画的复杂度

### 4. 可访问性

- 尊重用户的 `prefers-reduced-motion` 设置
- 确保动画不会影响内容的可读性

## 配置选项

### 动画时长和缓动

所有动画组件都支持自定义时长和缓动函数：

```tsx
// 自定义过渡效果
const customTransition = {
  type: 'spring',
  stiffness: 100,
  damping: 15,
  duration: 0.5
};
```

### 动画方向

支持的动画方向：
- `up` - 从下方滑入
- `down` - 从上方滑入  
- `left` - 从右侧滑入
- `right` - 从左侧滑入

## 故障排除

### 常见问题

1. **动画不显示**
   - 确保组件被正确导入
   - 检查是否在客户端组件中使用（添加 `'use client'`）

2. **导入被自动移除**
   - 确保在添加导入的同时使用这些组件
   - 检查 ESLint 和 Prettier 配置

3. **性能问题**
   - 减少同时运行的动画数量
   - 使用 `will-change` CSS 属性优化性能

## 下一步

1. 为其他首页组件添加动画效果
2. 创建页面间的过渡动画
3. 添加加载状态的动画效果
4. 实现响应式动画配置
