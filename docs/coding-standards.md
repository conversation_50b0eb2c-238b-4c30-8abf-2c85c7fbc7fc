# 代码规范文档

## 概述

本文档定义了防汛设备企业网站项目的代码规范和开发流程，确保代码质量、可维护性和团队协作效率。

## 技术栈

- **框架**: Next.js 14.2.0 (App Router)
- **语言**: TypeScript 5.4.5
- **UI库**: Tailwind CSS + Shadcn UI
- **内容管理**: Contentlayer
- **国际化**: next-intl
- **包管理**: pnpm

## 代码风格

### 1. 命名规范

#### 文件和目录命名
- 使用 kebab-case（短横线分隔）：`user-profile.tsx`
- 组件文件使用 PascalCase：`UserProfile.tsx`
- 页面文件使用 kebab-case：`about-us/page.tsx`
- 工具函数文件使用 kebab-case：`format-date.ts`

#### 变量和函数命名
- 使用 camelCase：`userName`, `getUserData()`
- 布尔值使用描述性前缀：`isLoading`, `hasError`, `canEdit`
- 常量使用 UPPER_SNAKE_CASE：`API_BASE_URL`, `MAX_FILE_SIZE`

#### 组件命名
- 使用 PascalCase：`UserProfile`, `NavigationMenu`
- 描述性命名，避免缩写：`Button` 而不是 `Btn`

### 2. TypeScript 规范

#### 类型定义
```typescript
// 使用 interface 定义对象类型
interface User {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
}

// 使用 type 定义联合类型或复杂类型
type Status = 'pending' | 'approved' | 'rejected';
type UserWithStatus = User & { status: Status };
```

#### 泛型使用
```typescript
// 明确的泛型约束
interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

// 函数泛型
function processData<T extends Record<string, unknown>>(data: T): T {
  return data;
}
```

### 3. React 组件规范

#### 函数组件结构
```typescript
import { FC, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  children: ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary';
}

export const Component: FC<ComponentProps> = ({
  children,
  className,
  variant = 'primary',
}) => {
  return (
    <div className={cn('base-styles', variant === 'primary' && 'primary-styles', className)}>
      {children}
    </div>
  );
};
```

#### Hooks 使用
- 自定义 hooks 以 `use` 开头
- 将相关逻辑封装到自定义 hooks 中
- 避免在组件中直接使用复杂的状态逻辑

### 4. 样式规范

#### Tailwind CSS
- 优先使用 Tailwind 工具类
- 使用 `cn()` 函数合并类名
- 复杂样式使用 CSS 变量和自定义类

#### 响应式设计
```typescript
// 移动优先的响应式设计
<div className="w-full md:w-1/2 lg:w-1/3">
  <div className="p-4 md:p-6 lg:p-8">
    Content
  </div>
</div>
```

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 国际化路由
│   └── globals.css        # 全局样式
├── components/            # 组件
│   ├── ui/               # 基础 UI 组件
│   ├── layout/           # 布局组件
│   ├── shared/           # 共享组件
│   └── forms/            # 表单组件
├── lib/                  # 工具函数
├── hooks/                # 自定义 hooks
├── types/                # 类型定义
└── middleware.ts         # 中间件
```

## 开发流程

### 1. 分支管理
- `main`: 生产分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 2. 提交规范
使用 Conventional Commits 格式：
```
feat: 添加用户认证功能
fix: 修复导航菜单响应式问题
docs: 更新 API 文档
style: 格式化代码
refactor: 重构用户组件
test: 添加单元测试
chore: 更新依赖包
```

### 3. 代码审查
- 所有代码必须通过 PR 审查
- 确保测试通过
- 检查代码风格和规范
- 验证功能完整性

## 工具配置

### 1. ESLint 规则
项目使用严格的 ESLint 配置，包括：
- TypeScript 规则
- React 最佳实践
- 可访问性检查
- Import 排序

### 2. Prettier 格式化
- 自动格式化代码
- 统一代码风格
- 保存时自动格式化

### 3. Husky + lint-staged
- 提交前自动检查
- 自动修复可修复的问题
- 确保代码质量

## 性能优化

### 1. 组件优化
- 使用 React.memo 优化重渲染
- 合理使用 useMemo 和 useCallback
- 避免在渲染函数中创建对象

### 2. 图片优化
- 使用 Next.js Image 组件
- 提供适当的 alt 文本
- 使用 WebP 格式

### 3. 代码分割
- 使用动态导入
- 路由级别的代码分割
- 组件级别的懒加载

## 测试规范

### 1. 单元测试
- 使用 Jest + React Testing Library
- 测试组件行为而非实现
- 保持测试简单和可读

### 2. 集成测试
- 测试组件间交互
- 测试用户流程
- 模拟真实使用场景

## 安全规范

### 1. 数据验证
- 客户端和服务端双重验证
- 使用 Zod 进行类型安全验证
- 防止 XSS 和 CSRF 攻击

### 2. 环境变量
- 敏感信息使用环境变量
- 区分开发和生产环境
- 不在代码中硬编码密钥

## 国际化规范

### 1. 翻译文件
- 使用结构化的翻译键
- 保持翻译文件同步
- 提供上下文信息

### 2. 内容管理
- 使用 Contentlayer 管理多语言内容
- 保持内容结构一致
- 提供默认语言回退

## 部署规范

### 1. 构建优化
- 启用生产模式优化
- 压缩静态资源
- 配置 CDN

### 2. 监控和日志
- 配置错误监控
- 记录关键操作
- 性能监控

## 文档规范

### 1. 代码注释
- 复杂逻辑添加注释
- 使用 JSDoc 注释函数
- 保持注释与代码同步

### 2. README 文档
- 项目介绍和设置说明
- 开发和部署指南
- 常见问题解答

## 总结

遵循这些规范可以确保：
- 代码质量和一致性
- 团队协作效率
- 项目可维护性
- 用户体验优化

所有团队成员都应该熟悉并遵循这些规范，在有疑问时及时沟通和讨论。
