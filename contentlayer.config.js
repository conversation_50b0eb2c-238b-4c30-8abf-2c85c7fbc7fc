import { defineDocumentType, makeSource } from 'contentlayer/source-files';
import readingTime from 'reading-time';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import rehypePrettyCode from 'rehype-pretty-code';
import rehypeSlug from 'rehype-slug';
import remarkGfm from 'remark-gfm';

// 产品内容类型
export const Product = defineDocumentType(() => ({
  name: 'Product',
  filePathPattern: `products/**/*.md`,
  fields: {
    title: {
      type: 'string',
      description: '产品名称',
      required: true,
    },
    description: {
      type: 'string',
      description: '产品描述',
      required: true,
    },
    date: {
      type: 'date',
      description: '发布日期',
      required: true,
    },
    category: {
      type: 'string',
      description: '产品分类',
      required: true,
    },
    featured: {
      type: 'boolean',
      description: '是否为特色产品',
      required: false,
      default: false,
    },
    images: {
      type: 'list',
      of: { type: 'string' },
      description: '产品图片列表',
      required: false,
    },
    specifications: {
      type: 'json',
      description: '技术规格',
      required: false,
    },
    applications: {
      type: 'list',
      of: { type: 'string' },
      description: '应用场景',
      required: false,
    },
    certifications: {
      type: 'list',
      of: { type: 'string' },
      description: '认证信息',
      required: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (product) => {
        const pathParts = product._raw.flattenedPath.split('/');
        const locale = pathParts[1]; // products/zh/product-name -> zh
        const slug = pathParts[2]; // products/zh/product-name -> product-name
        return `/${locale}/products/${slug}`;
      },
    },
    locale: {
      type: 'string',
      resolve: (product) => {
        const pathParts = product._raw.flattenedPath.split('/');
        return pathParts[1]; // products/zh/product-name -> zh
      },
    },
    slug: {
      type: 'string',
      resolve: (product) => {
        const pathParts = product._raw.flattenedPath.split('/');
        return pathParts[2]; // products/zh/product-name -> product-name
      },
    },
    readingTime: {
      type: 'json',
      resolve: (product) => readingTime(product.body.raw),
    },
  },
}));

// 案例内容类型
export const Case = defineDocumentType(() => ({
  name: 'Case',
  filePathPattern: `cases/**/*.md`,
  fields: {
    title: {
      type: 'string',
      description: '案例标题',
      required: true,
    },
    description: {
      type: 'string',
      description: '案例描述',
      required: true,
    },
    date: {
      type: 'date',
      description: '项目日期',
      required: true,
    },
    location: {
      type: 'string',
      description: '项目地点',
      required: false,
    },
    client: {
      type: 'string',
      description: '客户名称',
      required: false,
    },
    challenge: {
      type: 'string',
      description: '面临挑战',
      required: false,
    },
    solution: {
      type: 'string',
      description: '解决方案',
      required: false,
    },
    results: {
      type: 'string',
      description: '项目成果',
      required: false,
    },
    images: {
      type: 'list',
      of: { type: 'string' },
      description: '案例图片',
      required: false,
    },
    featured: {
      type: 'boolean',
      description: '是否为特色案例',
      required: false,
      default: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (caseItem) => {
        const pathParts = caseItem._raw.flattenedPath.split('/');
        const locale = pathParts[1]; // cases/zh/case-name -> zh
        const slug = pathParts[2]; // cases/zh/case-name -> case-name
        return `/${locale}/cases/${slug}`;
      },
    },
    locale: {
      type: 'string',
      resolve: (caseItem) => {
        const pathParts = caseItem._raw.flattenedPath.split('/');
        return pathParts[1]; // cases/zh/case-name -> zh
      },
    },
    slug: {
      type: 'string',
      resolve: (caseItem) => {
        const pathParts = caseItem._raw.flattenedPath.split('/');
        return pathParts[2]; // cases/zh/case-name -> case-name
      },
    },
    readingTime: {
      type: 'json',
      resolve: (caseItem) => readingTime(caseItem.body.raw),
    },
  },
}));

// 新闻内容类型
export const News = defineDocumentType(() => ({
  name: 'News',
  filePathPattern: `news/**/*.md`,
  fields: {
    title: {
      type: 'string',
      description: '新闻标题',
      required: true,
    },
    description: {
      type: 'string',
      description: '新闻描述',
      required: true,
    },
    date: {
      type: 'date',
      description: '发布日期',
      required: true,
    },
    author: {
      type: 'string',
      description: '作者',
      required: false,
    },
    category: {
      type: 'string',
      description: '新闻分类',
      required: false,
    },
    excerpt: {
      type: 'string',
      description: '新闻摘要',
      required: false,
    },
    tags: {
      type: 'list',
      of: { type: 'string' },
      description: '标签',
      required: false,
    },
    featured: {
      type: 'boolean',
      description: '是否为特色新闻',
      required: false,
      default: false,
    },
    image: {
      type: 'string',
      description: '封面图片',
      required: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (news) => {
        const pathParts = news._raw.flattenedPath.split('/');
        const locale = pathParts[1]; // news/zh/news-title -> zh
        const slug = pathParts[2]; // news/zh/news-title -> news-title
        return `/${locale}/news/${slug}`;
      },
    },
    locale: {
      type: 'string',
      resolve: (news) => {
        const pathParts = news._raw.flattenedPath.split('/');
        return pathParts[1]; // news/zh/news-title -> zh
      },
    },
    slug: {
      type: 'string',
      resolve: (news) => {
        const pathParts = news._raw.flattenedPath.split('/');
        return pathParts[2]; // news/zh/news-title -> news-title
      },
    },
    readingTime: {
      type: 'json',
      resolve: (news) => readingTime(news.body.raw),
    },
  },
}));

// 文章内容类型（统一管理信息文章）
export const Article = defineDocumentType(() => ({
  name: 'Article',
  filePathPattern: `blog/**/*.md`,
  fields: {
    title: {
      type: 'string',
      description: '文章标题',
      required: true,
    },
    excerpt: {
      type: 'string',
      description: '文章摘要',
      required: true,
    },
    author: {
      type: 'string',
      description: '作者',
      required: false,
    },
    publishDate: {
      type: 'date',
      description: '发布日期',
      required: true,
    },
    tags: {
      type: 'list',
      of: {
        type: 'enum',
        options: [
          '案例分享',
          '行业新闻',
          '企业动态',
          '灾害新闻',
          'case-study',
          'industry-news',
          'company-news',
          'disaster-news',
        ],
      },
      description: '文章标签',
      required: true,
    },
    featured: {
      type: 'boolean',
      description: '是否为特色文章',
      required: false,
      default: false,
    },
    featuredImage: {
      type: 'string',
      description: '特色图片',
      required: false,
    },
    // 从原有内容类型迁移的字段
    location: {
      type: 'string',
      description: '项目地点（案例分享类文章）',
      required: false,
    },
    client: {
      type: 'string',
      description: '客户名称（案例分享类文章）',
      required: false,
    },
    challenge: {
      type: 'string',
      description: '面临挑战（案例分享类文章）',
      required: false,
    },
    solution: {
      type: 'string',
      description: '解决方案（案例分享类文章）',
      required: false,
    },
    results: {
      type: 'string',
      description: '项目成果（案例分享类文章）',
      required: false,
    },
    category: {
      type: 'string',
      description: '文章分类',
      required: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (article) => {
        const pathParts = article._raw.flattenedPath.split('/');
        const locale = pathParts[1]; // blog/en/article-name -> en
        const slug = pathParts[2]; // blog/en/article-name -> article-name
        return `/${locale}/blog/${slug}`;
      },
    },
    locale: {
      type: 'string',
      resolve: (article) => {
        const pathParts = article._raw.flattenedPath.split('/');
        return pathParts[1]; // blog/en/article-name -> en
      },
    },
    slug: {
      type: 'string',
      resolve: (article) => {
        const pathParts = article._raw.flattenedPath.split('/');
        return pathParts[2]; // blog/en/article-name -> article-name
      },
    },
    readingTime: {
      type: 'json',
      resolve: (article) => readingTime(article.body.raw),
    },
  },
}));

// 页面内容类型
export const Page = defineDocumentType(() => ({
  name: 'Page',
  filePathPattern: `pages/**/*.md`,
  fields: {
    title: {
      type: 'string',
      description: '页面标题',
      required: true,
    },
    description: {
      type: 'string',
      description: '页面描述',
      required: true,
    },
    slug: {
      type: 'string',
      description: '页面路径',
      required: true,
    },
    layout: {
      type: 'string',
      description: '页面布局',
      required: false,
      default: 'default',
    },
    sections: {
      type: 'json',
      description: '页面区块配置',
      required: false,
    },
  },
  computedFields: {
    url: {
      type: 'string',
      resolve: (page) => {
        const pathParts = page._raw.flattenedPath.split('/');
        const locale = pathParts[1]; // pages/zh/about -> zh
        return `/${locale}/${page.slug}`;
      },
    },
    locale: {
      type: 'string',
      resolve: (page) => {
        const pathParts = page._raw.flattenedPath.split('/');
        return pathParts[1]; // pages/zh/about -> zh
      },
    },
    readingTime: {
      type: 'json',
      resolve: (page) => readingTime(page.body.raw),
    },
  },
}));

export default makeSource({
  contentDirPath: 'content',
  documentTypes: [Product, Article, Page],
  disableImportAliasWarning: true,
  mdx: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [
      rehypeSlug,
      [
        rehypeAutolinkHeadings,
        {
          behavior: 'wrap',
          properties: {
            className: ['anchor'],
          },
        },
      ],
      [
        rehypePrettyCode,
        {
          theme: 'github-dark',
          keepBackground: false,
        },
      ],
    ],
  },
});
