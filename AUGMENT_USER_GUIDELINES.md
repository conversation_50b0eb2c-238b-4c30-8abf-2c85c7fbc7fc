# Augment User Guidelines for Flood Control Equipment Website

## Project-Specific Instructions

### Language and Communication
- Always respond in simplified Chinese when discussing project requirements or providing explanations
- Use English for all code comments, variable names, and technical documentation
- When explaining code functionality, provide both English technical terms and Chinese explanations

### Internationalization Priority
- Treat internationalization as the highest priority in all development tasks
- Before writing any component, always consider how text will be translated
- When suggesting code changes, always include translation key updates for both zh.json and en.json
- Flag any hardcoded text as a critical error that must be fixed immediately

### Code Quality Standards
- Always suggest TypeScript interfaces for component props, even for simple components
- Recommend React Server Components by default unless client-side interactivity is specifically needed
- When suggesting Tailwind classes, prioritize responsive design with mobile-first approach
- Always include proper error handling and loading states in component suggestions

### Content Management Approach
- For short UI text: Always use next-intl translation keys
- For long content (blog posts, product descriptions): Use separate Contentlayer files per language
- For dynamic content with variables: Always use parametrized translations
- For SEO content: Always use translation functions in metadata generation

### Development Workflow Preferences
- When creating new components, always provide the complete file structure including imports
- Include TypeScript type definitions for all props and return values
- Suggest proper file naming and organization according to the established structure
- Always consider accessibility (a11y) requirements and include proper ARIA labels using translations

### Error Prevention Focus
- Always check for potential hardcoded text in suggested code
- Verify that translation keys exist in both language files before suggesting their use
- Ensure all image alt texts use translation functions
- Check that form labels, placeholders, and validation messages are translatable

### Performance Considerations
- Suggest React.memo for components that might re-render frequently
- Recommend proper image optimization using Next.js Image component
- Consider code splitting for large components or features
- Always suggest proper caching strategies for API calls or data fetching

### Testing Recommendations
- Always suggest testing both language versions when proposing new features
- Include responsive design testing in recommendations
- Suggest accessibility testing for interactive components
- Recommend translation completeness checks for new features

### Specific Technology Guidance
- For styling: Always use Tailwind CSS classes, never suggest custom CSS
- For UI components: Always use Shadcn UI components from @/components/ui
- For forms: Suggest React Hook Form with Zod validation
- For animations: Recommend Tailwind CSS animations or Framer Motion for complex cases
- For icons: Use Lucide React icons consistently

### Project Context Awareness
- Remember this is a B2B flood control equipment website targeting professional buyers
- Content should be professional and technical in nature
- Consider both domestic Chinese market and international English-speaking markets
- Focus on conversion optimization and lead generation in suggestions

### Code Review Mindset
- Always review suggested code for internationalization compliance
- Check for proper TypeScript typing and error handling
- Verify responsive design implementation
- Ensure accessibility standards are met
- Confirm proper file organization and naming conventions

### Documentation Standards
- Provide clear explanations for complex logic or business rules
- Include usage examples for custom hooks or utility functions
- Document any project-specific patterns or conventions
- Explain the reasoning behind architectural decisions

### Deployment and Maintenance
- Consider the impact of changes on both language versions
- Suggest proper testing procedures for multilingual features
- Recommend monitoring and error tracking for production issues
- Consider SEO implications of suggested changes for both languages
