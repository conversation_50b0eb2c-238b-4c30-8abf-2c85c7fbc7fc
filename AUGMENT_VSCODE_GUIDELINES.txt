For this Next.js 14 multilingual flood control equipment website:

- Always use TypeScript with strict typing, never 'any' type
- Use Next.js 14 App Router with React Server Components by default
- Use Tailwind CSS for all styling, never inline styles or CSS-in-JS
- Use Shadcn UI components from @/components/ui, never modify them directly
- Use next-intl for internationalization, never hardcode any display text
- Use Contentlayer for content management with separate files per language
- Use pnpm as package manager, never npm or yarn
- Write all code comments in English, never Chinese
- Always use translation functions t('namespace.key') for any UI text
- Keep translation files synchronized between zh.json and en.json
- Use parametrized translations for dynamic content with variables
- Implement responsive design with mobile-first approach
- Always provide proper alt text for images using translations
- Use proper TypeScript interfaces for all component props
- Include error handling and loading states in all components
- Test both Chinese and English versions of all features
- Generate proper metadata for each page using translations
- Use kebab-case for file names, PascalCase for components
- Place components in appropriate directories under src/components/
- Never use conditional locale rendering for display text
- Always suggest React.memo, useMemo, useCallback when appropriate
- Prioritize accessibility with proper ARIA labels using translations
- Use Next.js Image component for all images with proper optimization
- Implement proper error boundaries and user-friendly error messages
- Validate all forms using Zod with translatable error messages
- Consider SEO implications for both languages in all suggestions
- Flag any hardcoded text as critical error requiring immediate fix
- Always explain code functionality in simplified Chinese when requested
- Provide complete file structure including imports for new components
- Suggest proper caching strategies for API calls and data fetching
